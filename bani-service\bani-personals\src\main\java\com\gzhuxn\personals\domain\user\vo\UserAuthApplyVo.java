package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserAuthApply;
import com.gzhuxn.personals.enums.audit.AuditType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 用户-认证申请视图对象 user_auth_apply
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserAuthApply.class, convertGenerate = false)
public class UserAuthApplyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 认证类型;101-实名认证、2-学历认证、3-车辆认证、4-房屋认证
     *
     * @see AuditType
     */
    @ExcelProperty(value = "认证类型")
    private Integer type;

    /**
     * 认证状态;1待认证、2已认证、3认证失败
     */
    @ExcelProperty(value = "认证状态")
    private Integer status;

    /**
     * 失败描述
     */
    @ExcelProperty(value = "失败描述")
    private String auditDesc;

    /**
     * 认证账户ID
     */
    @ExcelProperty(value = "认证账户ID")
    private Long authUserId;

    /**
     * 认证时间
     */
    @ExcelProperty(value = "认证时间")
    private LocalDateTime authTime;

    /**
     * Json数据
     */
    @ExcelProperty(value = "Json数据")
    private String contentJs;
}
