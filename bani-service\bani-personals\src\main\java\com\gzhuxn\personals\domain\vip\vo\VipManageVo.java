package com.gzhuxn.personals.domain.vip.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.vip.VipManage;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 会员-会员管理视图对象 vip_manage
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = VipManage.class, convertGenerate = false)
public class VipManageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 会员月份数
     */
    @ExcelProperty(value = "会员月份数")
    private Integer months;

    /**
     * 原费用
     */
    @ExcelProperty(value = "原费用")
    private BigDecimal originalAmount;

    /**
     * 实际支付的费用
     */
    @ExcelProperty(value = "实际支付的费用")
    private BigDecimal amount;

    /**
     * 兑换花瓣数量，可提现
     */
    @ExcelProperty(value = "兑换花瓣数量，可提现")
    private Integer coin;

    /**
     * 已售卖数量
     */
    @ExcelProperty(value = "已售卖数量")
    private Integer sellNum;

    /**
     * 可用状态：0未启用、1已启用
     */
    @ExcelProperty(value = "可用状态：0未启用、1已启用")
    private Integer status;


}
