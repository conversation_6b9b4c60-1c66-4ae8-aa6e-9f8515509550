package com.gzhuxn.personals.domain.message.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.message.MsgContent;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 消息-消息内容视图对象 msg_content
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MsgContent.class, convertGenerate = false)
public class MsgContentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 类型;1系统通知、2关注我的、3与我相关
     */
    @ExcelProperty(value = "类型;1系统通知、2关注我的、3与我相关")
    private Integer type;

    /**
     * 子类型;31点赞、32微信申请、33送礼物、34我的问答
     */
    @ExcelProperty(value = "子类型;31点赞、32微信申请、33送礼物、34我的问答")
    private Integer subType;

    /**
     * 消息内容
     */
    @ExcelProperty(value = "消息内容")
    private String content;


}
