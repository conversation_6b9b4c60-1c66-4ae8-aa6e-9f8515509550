package com.gzhuxn.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.core.utils.StringUtils;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.system.domain.AppAgreement;
import com.gzhuxn.system.domain.bo.AppAgreementBo;
import com.gzhuxn.system.domain.vo.AppAgreementVo;

import java.util.Map;

/**
 * app应用-协议配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
public interface AppAgreementMapper extends BaseMapperPlus<AppAgreement, AppAgreementVo> {

    default LambdaQueryWrapper<AppAgreement> buildQueryWrapper(AppAgreementBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppAgreement> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppAgreement::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getAgreementKey()), AppAgreement::getAgreementKey, bo.getAgreementKey());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), AppAgreement::getContent, bo.getContent());
        lqw.eq(bo.getStatus() != null, AppAgreement::getStatus, bo.getStatus());
        lqw.orderByDesc(AppAgreement::getCreateTime);
        return lqw;
    }

    /**
     * 校验协议Key是否唯一
     *
     * @param entity et
     * @return true/false
     */
    default boolean validateAgreementKeyUnique(AppAgreement entity) {
        if (entity.getId() == null) {
            return selectCount(Wrappers.<AppAgreement>lambdaQuery()
                .eq(AppAgreement::getAgreementKey, entity.getAgreementKey())) == 0;
        }
        return selectCount(Wrappers.<AppAgreement>lambdaQuery()
            .eq(AppAgreement::getAgreementKey, entity.getAgreementKey())
            .ne(AppAgreement::getId, entity.getId())) == 0;
    }
}
