package com.gzhuxn.system.service;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.system.controller.admin.app.bo.AdminAppAgreementStatusUpBo;
import com.gzhuxn.system.controller.admin.app.vo.AdminAppAgreementPageVo;
import com.gzhuxn.system.domain.bo.AppAgreementBo;
import com.gzhuxn.system.domain.vo.AppAgreementVo;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;

/**
 * app应用-协议配置Service接口
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
public interface IAppAgreementService {

    /**
     * 查询基础-协议配置
     *
     * @param id 主键
     * @return 基础-协议配置
     */
    AppAgreementVo queryAdminById(Long id);

    /**
     * 分页查询基础-协议配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 基础-协议配置分页列表
     */
    TableDataInfo<AdminAppAgreementPageVo> queryAdminPageList(AppAgreementBo bo, PageQuery pageQuery);


    /**
     * 新增基础-协议配置
     *
     * @param bo 基础-协议配置
     * @return 是否新增成功
     */
    boolean insertAdminByBo(AppAgreementBo bo);

    /**
     * 修改基础-协议配置
     *
     * @param bo 基础-协议配置
     * @return 是否修改成功
     */
    boolean updateAdminByBo(AppAgreementBo bo);

    /**
     * 修改基础-协议配置状态
     *
     * @param bo 待修改的基础-协议配置
     * @return 是否修改成功
     */
    boolean updateAdminStatus(AdminAppAgreementStatusUpBo bo);

    /**
     * 校验并批量删除基础-协议配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据协议Key查询协议配置
     *
     * @param key 协议Key
     * @return 协议配置
     */
    AppAgreementVo getProtocolContentByKey(@NotNull(message = "协议Key不能为空") String key);
}
