package com.gzhuxn.personals.service.audit.processor;

import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessor;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;
import com.gzhuxn.personals.service.activity.IActivityService;
import com.gzhuxn.resource.api.RemoteContentAuditService;
import com.gzhuxn.resource.api.domain.RemoteCheckText;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 活动-活动管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RequiredArgsConstructor
@Service
public class AuditActivityProcessor implements ContentAuditProcessor {
    @DubboReference
    private RemoteContentAuditService remoteContentAuditService;

    @Resource
    private IActivityService activityService;

    @Override
    public AuditType getAuditType() {
        return AuditType.ACTIVITY;
    }

    @Override
    public ContentAuditRes execute(Long businessId) {
        Activity activity = activityService.existById(businessId);
        return check(activity);
    }

    public ContentAuditRes check(Activity activity) {
        // 审核介绍
        RemoteCheckText checkText = remoteContentAuditService.checkText(activity.getName(), activity.getIntroduce(), activity.getAddr());
        // 审核通过则发布
        if (checkText.isPass()) {
            activity.setAuditStatus(AuditStatus.PASS.getValue());
            activityService.publish(activity);
            return ContentAuditRes.isOk();
        }

        // 审核不通过
        String failMag = checkText.failMag();
        activity.setAuditStatus(AuditStatus.REJECT.getValue());
        activityService.updateAuditFail(activity);
        return ContentAuditRes.reject(failMag);
    }
}
