package com.gzhuxn.personals.service.activity.impl;

import cn.hutool.json.JSONUtil;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.base.utils.Func;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.core.utils.StreamUtils;
import com.gzhuxn.personals.controller.app.activity.bo.AppActivityFeeBo;
import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.domain.activity.ActivityFee;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardStorageVo;
import com.gzhuxn.personals.enums.activity.ActivityFeeType;
import com.gzhuxn.personals.mapper.activity.ActivityFeeMapper;
import com.gzhuxn.personals.service.activity.IActSafeguardService;
import com.gzhuxn.personals.service.activity.IActivityFeeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 活动-活动费用Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RequiredArgsConstructor
@Service
public class ActivityFeeServiceImpl
    extends BaniServiceImpl<ActivityFeeMapper, ActivityFee> implements IActivityFeeService {
    private final IActSafeguardService actSafeguardService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Activity activityEntity, List<AppActivityFeeBo> fees) {
        AssertUtils.notEmpty(fees, "费用信息不能为空!");
        List<ActivityFee> entityList = MapstructUtils.convert(fees, ActivityFee.class);

        // 删除不存在的
        List<ActivityFee> existFees = baseMapper.listByActivityId(activityEntity.getId());
        List<Long> removeIds = Func.subtractToMapper(existFees, entityList, ActivityFee::getId);
        super.removeBatchByIds(removeIds);

        // 计算项目总费用
        BigDecimal totalAmount = StreamUtils.getSumValue(entityList, ActivityFee::getAmount, BigDecimal::add, BigDecimal.ZERO);
        AssertUtils.isTrue(activityEntity.getAmount().equals(totalAmount), "费用信息计算有误，请刷新页面！");

        // 构造数据
        List<ActivityFee> saveList = entityList.stream().peek(fee -> {
            fee.setActivityId(activityEntity.getId());
            // 转换费用信息
            convertSafeguardFee(fee);
        }).toList();
        super.saveOrUpdateBatch(saveList);
    }

    /**
     * 转换费用信息
     *
     * @param fee 费用信息
     */
    private void convertSafeguardFee(ActivityFee fee) {
        // 保障费
        if (ActivityFeeType.SAFEGUARD.getValue().equals(fee.getType())) {
            ActSafeguardStorageVo safeguardVo = actSafeguardService.getStorageVoById(fee.getBusinessId());
            AssertUtils.isTrue(fee.getAmount().equals(safeguardVo.getAmount()), "保障费用有误，请刷新页面！");

            // 持久化保障信息
            fee.setContentJs(JSONUtil.toJsonStr(safeguardVo));
        }
    }

    @Override
    public List<ActivityFee> listByActivityId(Long id) {
        return baseMapper.listByActivityId(id);
    }
}
