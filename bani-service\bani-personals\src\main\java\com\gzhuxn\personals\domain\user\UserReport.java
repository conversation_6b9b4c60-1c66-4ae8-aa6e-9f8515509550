package com.gzhuxn.personals.domain.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.personals.domain.UserBaseEntity;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户-举报对象 user_report
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_report")
public class UserReport extends UserBaseEntity {
    /**
     * 标题
     */
    private String title;

    /**
     * 举报类型;1用户、2活动、3群组
     */
    private Integer type;

    /**
     * 关联业务ID
     */
    private Long businessId;

    /**
     * 内容
     */
    private String content;

    /**
     * 图片ID，最多6张
     */
    private String images;

    /**
     * 处理状态;1待处理、2已处理
     */
    private Integer status;

    /**
     * 审核状态
     *
     * @see AuditStatus
     */
    private Integer auditStatus;
}
