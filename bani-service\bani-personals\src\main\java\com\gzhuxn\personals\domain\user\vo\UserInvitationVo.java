package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserInvitation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-邀请记录视图对象 user_invitation
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserInvitation.class, convertGenerate = false)
public class UserInvitationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 邀请者用户ID
     */
    @ExcelProperty(value = "邀请者用户ID")
    private Long userId;

    /**
     * 被邀请者用户ID
     */
    @ExcelProperty(value = "被邀请者用户ID")
    private Long oppositeUserId;


}
