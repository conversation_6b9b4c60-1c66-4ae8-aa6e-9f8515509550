package com.gzhuxn.personals.service.user.impl;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.controller.app.user.bo.gift.AppUserGiftBo;
import com.gzhuxn.personals.controller.app.user.bo.gift.AppUserGiftCreateBo;
import com.gzhuxn.personals.controller.app.user.vo.gift.AppUserGiftVo;
import com.gzhuxn.personals.domain.gift.GiftManage;
import com.gzhuxn.personals.domain.user.UserGift;
import com.gzhuxn.personals.domain.user.bo.UserAccountUpdateBo;
import com.gzhuxn.personals.domain.user.bo.UserGiftBo;
import com.gzhuxn.personals.domain.user.vo.UserGiftVo;
import com.gzhuxn.personals.enums.user.account.AccountUpType;
import com.gzhuxn.personals.mapper.user.UserGiftMapper;
import com.gzhuxn.personals.service.gift.IGiftManageService;
import com.gzhuxn.personals.service.user.IUserAccountService;
import com.gzhuxn.personals.service.user.IUserGiftService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户-发出的礼物Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@RequiredArgsConstructor
@Service
public class UserGiftServiceImpl extends ServiceImpl<UserGiftMapper, UserGift> implements IUserGiftService {
    private final IGiftManageService giftManageService;

    private final IUserAccountService userAccountService;

    @Override
    public UserGiftVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public TableDataInfo<UserGiftVo> queryPageList(UserGiftBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserGift> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserGiftVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<AppUserGiftVo> queryAppPageList(AppUserGiftBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserGift> lqw = Wrappers.lambdaQuery();
        Page<AppUserGiftVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, AppUserGiftVo.class);
        return TableDataInfo.build(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = "#bo.userId", expire = 60000, acquireTimeout = 1000)
    public boolean insertByBo(AppUserGiftCreateBo bo) {
        UserGift add = MapstructUtils.convert(bo, UserGift.class);
        validEntityBeforeSave(add);

        // 保存数据
        save(add);

        // 更新用户账户
        return userAccountService.update(AccountUpType.GIFT_OUT, UserAccountUpdateBo.builder()
            .userId(add.getUserId())
            .oppositeUserId(add.getOppositeUserId())
            .businessId(add.getId())
            .coin(add.getGiftNum() * add.getGiftPrice())
            .build());
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(UserGift entity) {
        GiftManage giftManage = giftManageService.getById(entity.getGiftId());
        AssertUtils.notNull(giftManage, "礼物不存在，请刷新页面！");

        AssertUtils.isTrue(giftManage.getPrice().equals(entity.getGiftPrice()), "礼物价格已发生变化，请刷新页面！");

        // 计算总花瓣
        int totalCoin = entity.getGiftPrice() * entity.getGiftNum();

        // 判断用户花瓣是否充足
        boolean isEnough = userAccountService.isEnough(entity.getUserId(), totalCoin);
        AssertUtils.isTrue(isEnough, "{coin.name}不足，请充值！");
    }

    @Override
    public List<String> queryUsedByIds(List<Long> giftIds) {
        return baseMapper.queryUsedByIds(giftIds);
    }
}
