package com.gzhuxn.system.domain.bo;

import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.system.domain.AppHelp;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * app应用-常见问题帮助业务对象 app_help
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AppHelp.class, reverseConvertGenerate = false)
public class AppHelpBo extends BsBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 帮助key
     */
    private String helpKey;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 内容Html
     */
    @NotBlank(message = "内容Html不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer sort;

    /**
     * 状态;0无效、1有效。备注：一个key 状态status=1时只能存在一条数据
     *
     * @see com.gzhuxn.common.core.enums.EnableStatus
     */
    @NotNull(message = "状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer status;
}
