package com.gzhuxn.personals.domain.group.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.group.GroupUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 群组-群组成员视图对象 grp_group_user
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GroupUser.class, convertGenerate = false)
public class GroupUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 群组ID
     */
    @ExcelProperty(value = "群组ID")
    private Long groupId;

    /**
     * 成员ID
     */
    @ExcelProperty(value = "成员ID")
    private Long userId;

    /**
     * 管理员标识;1群主、2管理员、3成员
     */
    @ExcelProperty(value = "管理员标识;1群主、2管理员、3成员")
    private Integer adminFlag;


}
