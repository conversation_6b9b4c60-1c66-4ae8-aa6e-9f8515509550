package com.gzhuxn.resource.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.facebody20200910.models.ExecuteServerSideVerificationRequest;
import com.aliyun.facebody20200910.models.ExecuteServerSideVerificationResponse;
import com.aliyun.imageaudit20191230.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.gzhuxn.common.base.utils.Func;
import com.gzhuxn.common.core.exception.NativeException;
import com.gzhuxn.common.core.exception.ServiceException;
import com.gzhuxn.resource.api.RemoteContentAuditService;
import com.gzhuxn.resource.api.domain.RemoteCheckIdentity;
import com.gzhuxn.resource.api.domain.RemoteCheckImage;
import com.gzhuxn.resource.api.domain.RemoteCheckText;
import com.gzhuxn.resource.api.enums.CheckImageLabel;
import com.gzhuxn.resource.api.enums.CheckImageScene;
import com.gzhuxn.resource.api.enums.CheckTextLabel;
import com.gzhuxn.resource.prop.ContentAuditProperties;
import com.gzhuxn.resource.service.ISysOssService;
import com.gzhuxn.system.api.RemoteSensitiveWordService;
import com.gzhuxn.system.api.domain.vo.RemoteSensitiveWordVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Stream;

import static com.aliyun.facebody20200910.models.ExecuteServerSideVerificationResponseBody.ExecuteServerSideVerificationResponseBodyData;
import static com.aliyun.imageaudit20191230.models.ScanTextRequest.ScanTextRequestLabels;
import static com.aliyun.imageaudit20191230.models.ScanTextRequest.ScanTextRequestTasks;
import static com.aliyun.imageaudit20191230.models.ScanTextResponseBody.ScanTextResponseBodyDataElements;


/**
 * 内容审核服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@DubboService
public class RemoteContentAuditServiceImpl implements RemoteContentAuditService {
    @Resource
    private com.aliyun.imageaudit20191230.Client imageAuditClient;
    @Resource
    private com.aliyun.facebody20200910.Client faceBodyClient;
    @Resource
    private ContentAuditProperties auditProperties;
    @Lazy
    @Resource
    private ISysOssService ossService;
    @Resource
    private ContentAuditProperties contentAuditProperties;

    @DubboReference
    private RemoteSensitiveWordService remoteSensitiveWordService;

    private boolean isTextLabelLoad = false;
    private static final List<ScanTextRequestLabels> TEXT_LABELS = new ArrayList<>(8);

    private boolean isImageLabelLoad = false;
    private static final List<String> IMAGE_SCENES = new ArrayList<>(8);

    private static final RuntimeOptions RUNTIME_OPTIONS = new RuntimeOptions();

    /**
     * 敏感词缓存,默认一分钟
     */
    private final LoadingCache<String, AhoCorasick> localSensitiveWordsCache = Func.buildAsyncReloadingCache(Duration.ofSeconds(60L), new CacheLoader<>() {
        @Override
        public AhoCorasick load(@NotNull String key) {
            List<RemoteSensitiveWordVo> sensitiveWordVos = remoteSensitiveWordService.queryAll();
            if (sensitiveWordVos.isEmpty()) {
                return new AhoCorasick(Collections.emptyList());
            }
            return new AhoCorasick(sensitiveWordVos.stream().flatMap(vo -> vo.getWords().stream()).toList());
        }
    });

    /**
     * AC自动机实现敏感词匹配
     */
    private static class AhoCorasick {
        private final TrieNode root = new TrieNode();
        private boolean built = false;

        public AhoCorasick(List<String> words) {
            for (String word : words) {
                insert(word);
            }
            buildFailureLinks();
        }

        private void insert(String word) {
            TrieNode current = root;
            for (char c : word.toCharArray()) {
                current = current.children.computeIfAbsent(c, k -> new TrieNode());
            }
            current.isEnd = true;
            current.word = word;
        }

        /**
         * 构建失败指针
         */
        private void buildFailureLinks() {
            Queue<TrieNode> queue = new LinkedList<>();
            root.fail = root;
            queue.add(root);

            while (!queue.isEmpty()) {
                TrieNode current = queue.poll();
                for (Map.Entry<Character, TrieNode> entry : current.children.entrySet()) {
                    char c = entry.getKey();
                    TrieNode child = entry.getValue();

                    if (current == root) {
                        child.fail = root;
                    } else {
                        TrieNode failNode = current.fail;
                        while (failNode != root && !failNode.children.containsKey(c)) {
                            failNode = failNode.fail;
                        }
                        if (failNode.children.containsKey(c)) {
                            child.fail = failNode.children.get(c);
                        } else {
                            child.fail = root;
                        }
                    }
                    queue.add(child);
                }
            }
            built = true;
        }

        /**
         * 搜索文本中的敏感词
         *
         * @param text 待检测文本
         * @return 敏感词列表
         */
        public List<String> search(String text) {
            if (!built) {
                return Collections.emptyList();
            }

            List<String> foundWords = new ArrayList<>();
            TrieNode current = root;
            for (int i = 0; i < text.length(); i++) {
                char c = text.charAt(i);
                while (current != root && !current.children.containsKey(c)) {
                    current = current.fail;
                }
                if (current.children.containsKey(c)) {
                    current = current.children.get(c);
                    if (current.isEnd) {
                        foundWords.add(current.word);
                    }
                }
            }
            return foundWords;
        }

        /**
         * 树节点
         */
        private static class TrieNode {
            Map<Character, TrieNode> children = new HashMap<>();
            TrieNode fail;
            boolean isEnd;
            String word;
        }
    }

    // 初始化文本审核参数
    static {
        // 内置文本审核
        Stream.of(
            // 文字敏感
            CheckTextLabel.POLITICS,
            // 文字辱骂
            CheckTextLabel.ABUSE,
            // 文字暴恐
            CheckTextLabel.TERRORISM,
            // 文字鉴黄
            CheckTextLabel.PORN,
            // 文字违禁
            CheckTextLabel.CONTRABAND).forEach(label -> TEXT_LABELS.add(new ScanTextRequestLabels().setLabel(label.getLabel())));

        // 内置图片审核
        IMAGE_SCENES.add(CheckImageScene.PORN.getScene());
        IMAGE_SCENES.add(CheckImageScene.TERRORISM.getScene());
        IMAGE_SCENES.add(CheckImageScene.LIVE.getScene());
    }

    /**
     * 接口文档：
     * <a href="https://help.aliyun.com/zh/viapi/use-cases/the-text-content-security-1?spm=a2c4g.11186623.help-menu-142958.d_2_4_0.2053c39dsxj9Qj&scm=20140722.H_477836._.OR_help-T_cn~zh-V_1">...</a>
     */
    @Override
    public RemoteCheckText checkText(String... texts) {
        // 先进行本地检测
        RemoteCheckText checkText = localCheckText(texts);
        if (!checkText.isPass()) {
            return checkText;
        }

        // 如果未开启内容审核，则直接返回
        if (!contentAuditProperties.isEnabled()) {
            return checkText;
        }

        List<ScanTextRequestTasks> tasks = new ArrayList<>(texts.length);
        for (String text : texts) {
            tasks.add(new ScanTextRequestTasks().setContent(text));
        }
        ScanTextRequest request = new ScanTextRequest().setLabels(getTextLabels()).setTasks(tasks);
        try {
            // 复制代码运行请自行打印API的返回值
            ScanTextResponse r = imageAuditClient.scanTextWithOptions(request, RUNTIME_OPTIONS);
            Integer statusCode = r.getStatusCode();
            if (statusCode != 200) {
                throw new ServiceException("审核失败！");
            }
            List<ScanTextResponseBodyDataElements> elements = r.getBody().getData().getElements();
            List<RemoteCheckText.Element> retElements = new ArrayList<>(elements.size());
            int index = 0;
            for (ScanTextResponseBodyDataElements element : elements) {
                RemoteCheckText.Element retEle = convertTextEle(element);
                retEle.setIndex(index++);
                retElements.add(retEle);
            }
            return new RemoteCheckText(retElements);
        } catch (Exception e) {
            if (e instanceof TeaException) {
                log.error("审核失败！,error={}", JSONUtil.toJsonStr(e));
            }
            log.error("发起文本审核失败！", e);
            throw new ServiceException("发起文本审核失败！");
        }
    }

    // 转换文本审核结果
    private RemoteCheckText.Element convertTextEle(ScanTextResponseBodyDataElements element) {
        List<RemoteCheckText.Result> retElementResults = new ArrayList<>(element.getResults().size());
        for (ScanTextResponseBody.ScanTextResponseBodyDataElementsResults results : element.getResults()) {
            RemoteCheckText.Result retElementResult = new RemoteCheckText.Result();
            retElementResult.setPass(results.getSuggestion().contains("pass"));
            retElementResult.setRate(results.getRate());
            retElementResult.setLabel(CheckTextLabel.of(results.getLabel()));
            List<ScanTextResponseBody.ScanTextResponseBodyDataElementsResultsDetails> details = results.getDetails();
            if (null != details) {
                List<String> contents = new ArrayList<>();
                for (ScanTextResponseBody.ScanTextResponseBodyDataElementsResultsDetails detail : details) {
                    for (ScanTextResponseBody.ScanTextResponseBodyDataElementsResultsDetailsContexts context : detail.getContexts()) {
                        contents.add(context.getContext());
                    }
                }
                retElementResult.setContents(contents);
            }
            retElementResults.add(retElementResult);
        }
        return new RemoteCheckText.Element(retElementResults);
    }

    /**
     * 接口文档：
     * <a href="https://help.aliyun.com/zh/viapi/use-cases/image-content-security-1?spm=a2c4g.11186623.help-menu-142958.d_2_4_1.f5d014ec8CxN2a&scm=20140722.H_478892._.OR_help-T_cn~zh-V_1">...</a>
     */
    @Override
    public RemoteCheckImage checkImage(List<byte[]> bytes) {
        List<ScanImageAdvanceRequest.ScanImageAdvanceRequestTask> tasks = new ArrayList<>(bytes.size());
        for (byte[] byt : bytes) {
            ScanImageAdvanceRequest.ScanImageAdvanceRequestTask task = new ScanImageAdvanceRequest.ScanImageAdvanceRequestTask();
            task.setDataId(IdUtil.fastUUID());
            task.setImageTimeMillisecond(1L);
            task.setInterval(1);
            task.setMaxFrames(1);
            task.setImageURLObject(new ByteArrayInputStream(byt));
            tasks.add(task);
        }
        try {
            ScanImageAdvanceRequest request = new com.aliyun.imageaudit20191230.models.ScanImageAdvanceRequest().setTask(tasks).setScene(getImageScenes());
            ScanImageResponse r = imageAuditClient.scanImageAdvance(request, RUNTIME_OPTIONS);
            Integer statusCode = r.getStatusCode();
            if (statusCode != 200) {
                throw new ServiceException("审核失败！");
            }
            List<RemoteCheckImage.Element> retElements = new ArrayList<>(bytes.size());
            int index = 0;
            for (ScanImageResponseBody.ScanImageResponseBodyDataResults results : r.getBody().getData().getResults()) {
                RemoteCheckImage.Element element = convertImageEle(results);
                element.setIndex(index++);
                retElements.add(element);
            }
            return new RemoteCheckImage(retElements);
        } catch (Exception e) {
            if (e instanceof TeaException) {
                log.error("图片审核失败！,error={}", JSONUtil.toJsonStr(e));
            }
            log.error("发起图片审核失败！", e);
            throw new ServiceException("发起审核失败！");
        }
    }

    /**
     * 接口文档：
     * <a href="https://help.aliyun.com/zh/viapi/developer-reference/the-server-access-scheme?spm=a2c4g.11186623.help-menu-142958.d_4_3_3_7_0_0.1cd064923TCo3L&scm=20140722.H_465441._.OR_help-T_cn~zh-V_1">...</a>
     */
    @Override
    public RemoteCheckIdentity checkIdentity(String name, String idCard, Long ossId) {
        // 下载图片
        byte[] bytes = ossService.downloadBytes(ossId);
        RemoteCheckIdentity r = new RemoteCheckIdentity(name, idCard);
        ExecuteServerSideVerificationRequest request = new ExecuteServerSideVerificationRequest();
        request.setCertificateName(name);
        request.setCertificateNumber(idCard);

        // 人脸URL地址
        request.setFacialPictureData(Func.bytesToBase64(bytes));
        request.setSceneType("server");
        RuntimeOptions runtime = new RuntimeOptions();
        Map<String, String> headers = HashMap.newHashMap(4);
        headers.put("Accept-Encoding", "identity");
        try {
            ExecuteServerSideVerificationResponse response = faceBodyClient.executeServerSideVerificationWithOptions(request, headers, runtime);
            ExecuteServerSideVerificationResponseBodyData data = response.getBody().getData();
            if (null != data) {
                r.setPass(data.getPass());
                r.setReason(data.getReason());
            }
        } catch (Exception e) {
            log.error("实名认证识别失败！", e);
        }
        return r;
    }

    /**
     * 本地文本审核
     *
     * @param texts 文本内容
     * @return 是否通过
     */
    private RemoteCheckText localCheckText(String... texts) {
        try {
            List<RemoteCheckText.Element> retElements = new ArrayList<>(texts.length);
            AhoCorasick corasick = localSensitiveWordsCache.get(SENSITIVE_WORDS_CONFIG_KEY);
            for (String text : texts) {
                List<String> politics = new ArrayList<>();
                // 查询政治敏感词
                List<String> foundWords = corasick.search(text);
                if (CollUtil.isNotEmpty(foundWords)) {
                    politics.addAll(foundWords);
                }
                // 敏感词
                if (!politics.isEmpty()) {
                    RemoteCheckText.Result result = new RemoteCheckText.Result();
                    result.setPass(false);
                    result.setLabel(CheckTextLabel.POLITICS);
                    result.setContents(politics);
                    retElements.add(new RemoteCheckText.Element(result));
                }
            }
            return new RemoteCheckText(retElements);
        } catch (ExecutionException e) {
            throw new NativeException(e);
        }
    }

    /**
     * 转换图片审核结果
     *
     * @param results 图片审核结果
     * @return 图片审核结果
     */
    private RemoteCheckImage.Element convertImageEle(ScanImageResponseBody.ScanImageResponseBodyDataResults results) {
        List<RemoteCheckImage.Result> elementResults = new ArrayList<>(results.getSubResults().size());
        results.getSubResults().forEach(subResults -> {
            RemoteCheckImage.Result result = new RemoteCheckImage.Result();
            result.setPass(subResults.getSuggestion().contains("pass"));
            result.setRate(subResults.getRate());
            result.setLabel(CheckImageLabel.of(subResults.getLabel()));
            CheckImageScene scene = CheckImageScene.of(subResults.getScene());
            if (CheckImageScene.AD == scene) {

            }
            elementResults.add(result);
        });
        return new RemoteCheckImage.Element(elementResults);
    }

    /**
     * 获取文本审核标签
     *
     * @return 文本审核标签
     */
    private List<ScanTextRequestLabels> getTextLabels() {
        if (!isTextLabelLoad) {
            List<String> textLabels = auditProperties.getTextLabels();
            textLabels.forEach(label -> TEXT_LABELS.add(new ScanTextRequestLabels().setLabel(label)));
            isTextLabelLoad = true;
        }
        return TEXT_LABELS;
    }

    /**
     * 获取图片审核标签
     *
     * @return 图片审核标签
     */
    private List<String> getImageScenes() {
        if (!isImageLabelLoad) {
            List<String> imageScenes = auditProperties.getImageScenes();
            IMAGE_SCENES.addAll(imageScenes);
            isImageLabelLoad = true;
        }
        return IMAGE_SCENES;
    }


}
