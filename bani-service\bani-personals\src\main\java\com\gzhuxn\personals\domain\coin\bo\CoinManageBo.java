package com.gzhuxn.personals.domain.coin.bo;

import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.coin.CoinManage;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 花瓣-花瓣赠送管理业务对象 coin_manage
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CoinManage.class, reverseConvertGenerate = false)
public class CoinManageBo extends BsBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 类型;1.签到
     * 2.新手任务
     */
    @NotNull(message = "类型;1.签到 2.新手任务不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer type;

    /**
     * 子类型
     */
    @NotNull(message = "子类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer subType;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 花瓣数量
     */
    @NotNull(message = "花瓣数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer coin;
}
