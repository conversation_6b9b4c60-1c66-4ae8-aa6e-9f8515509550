package com.gzhuxn.personals.factory.audit;

import cn.hutool.extra.spring.SpringUtil;
import com.github.likavn.eventbus.core.utils.Assert;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.service.audit.processor.AuditTransferUserProcessor;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 审核处理器工厂
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/16 22:34
 */
public class ContentAuditProcessorFactory {
    private final ContentAuditProcessor defaultProcessor = SpringUtil.getBean(AuditTransferUserProcessor.class);

    //处理器
    private final Map<AuditType, ContentAuditProcessor> processorMap = new ConcurrentHashMap<>();

    /**
     * 注册处理器
     *
     * @param processor 处理器
     */
    public void register(ContentAuditProcessor processor) {
        Assert.isTrue(processor != null, "处理器不能为空！");

        Assert.isTrue(!processorMap.containsKey(processor.getAuditType()), "处理器已存在！");
        processorMap.put(processor.getAuditType(), processor);
    }

    /**
     * 获取处理器
     *
     * @param auditType 审核类型
     * @return 处理器
     */
    public Optional<ContentAuditProcessor> getProcessor(AuditType auditType) {
        ContentAuditProcessor processor = processorMap.get(auditType);
        if (processor == null) {
            processor = defaultProcessor;
        }
        return Optional.ofNullable(processor);
    }

}
