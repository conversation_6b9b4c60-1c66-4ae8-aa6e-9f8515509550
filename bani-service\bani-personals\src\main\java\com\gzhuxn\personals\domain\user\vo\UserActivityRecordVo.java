package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserActivityRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 用户-用户活动记录视图对象 user_activity_record
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserActivityRecord.class, convertGenerate = false)
public class UserActivityRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 支付订单ID
     */
    @ExcelProperty(value = "支付订单ID")
    private Long orderId;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 原费用
     */
    @ExcelProperty(value = "原费用")
    private BigDecimal originalAmount;

    /**
     * 实际支付的费用
     */
    @ExcelProperty(value = "实际支付的费用")
    private BigDecimal amount;

    /**
     * 兑换花瓣数量：平台赠送
     */
    @ExcelProperty(value = "兑换花瓣数量：平台赠送")
    private Integer coin;

    /**
     * 签到标识：0未签到、1已签到
     */
    @ExcelProperty(value = "签到标识：0未签到、1已签到")
    private Boolean signIn;

    /**
     * 签到时间
     */
    @ExcelProperty(value = "签到时间")
    private LocalDateTime signInTime;

    /**
     * 签到地址
     */
    @ExcelProperty(value = "签到地址")
    private String signInAddr;

    /**
     * 签到经度
     */
    @ExcelProperty(value = "签到经度")
    private Double signInLon;

    /**
     * 签到纬度
     */
    @ExcelProperty(value = "签到纬度")
    private Double signInLat;

    /**
     * 支付成功时间
     */
    @ExcelProperty(value = "支付成功时间")
    private LocalDateTime payTime;

    /**
     * 支付状态;1待支付、2支付中、3支付失败、4关闭、10支付成功
     *
     * @see com.gzhuxn.common.base.enums.PayStatus
     */
    @ExcelProperty(value = "支付状态;1待支付、2支付中、3支付失败、4关闭、10支付成功")
    private Integer payStatus;


}
