package com.gzhuxn.personals.domain.message.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.message.MsgGroupUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 消息-群聊组用户视图对象 msg_group_user
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MsgGroupUser.class, convertGenerate = false)
public class MsgGroupUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 群聊组ID
     */
    @ExcelProperty(value = "群聊组ID")
    private Long groupId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 名称;用户在群里的昵称/备注
     */
    @ExcelProperty(value = "名称;用户在群里的昵称/备注")
    private String name;

    /**
     * 类型：1群主/超管、2管理员、3群组成员
     */
    @ExcelProperty(value = "类型：1群主/超管、2管理员、3群组成员")
    private Integer type;

    /**
     * 是否开启打扰：0免打扰、1正常
     */
    @ExcelProperty(value = "是否开启打扰：0免打扰、1正常")
    private Boolean disturb;

    /**
     * 状态：1正常、2退群、3退群且删除群聊
     */
    @ExcelProperty(value = "状态：1正常、2退群、3退群且删除群聊")
    private Integer status;


}
