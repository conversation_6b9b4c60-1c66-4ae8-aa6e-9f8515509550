package com.gzhuxn.personals.controller.app.user;

import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.domain.user.bo.UserAuthApplyBo;
import com.gzhuxn.personals.domain.user.vo.UserAuthApplyVo;
import com.gzhuxn.personals.service.user.IUserAuthApplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户-认证信息
 * 前端访问路由地址为:/personals/user/authApply
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/authApply")
public class AppUserAuthApplyController extends BaseController {

    private final IUserAuthApplyService userAuthApplyService;

    /**
     * 获取用户-认证信息详细信息
     *
     * @param id 主键
     */
    @GetMapping("/detail")
    public R<UserAuthApplyVo> getInfo(@RequestParam Long id) {
        return R.ok(userAuthApplyService.queryById(id));
    }

    /**
     * 新增黑名单
     */
    @Log(title = "实名认证-添加", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create/identity")
    public R<Void> createIdentity(@Validated(AddGroup.class) @RequestBody UserAuthApplyBo.AuthIdentityBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        return toAjax(userAuthApplyService.createIdentity(bo));
    }

    /**
     * 新增黑名单
     */
    @Log(title = "学历认证-添加", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create/education")
    public R<Void> createEducation(@Validated(AddGroup.class) @RequestBody UserAuthApplyBo.AuthEducationBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        return toAjax(userAuthApplyService.createEducation(bo));
    }

    /**
     * 新增黑名单
     */
    @Log(title = "车辆认证-添加", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create/car")
    public R<Void> createCar(@Validated(AddGroup.class) @RequestBody UserAuthApplyBo.AuthCarBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        return toAjax(userAuthApplyService.createCar(bo));
    }

    /**
     * 新增黑名单
     */
    @Log(title = "房屋认证-添加", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create/house")
    public R<Void> createHouse(@Validated(AddGroup.class) @RequestBody UserAuthApplyBo.AuthHouseBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        return toAjax(userAuthApplyService.createHouse(bo));
    }
}
