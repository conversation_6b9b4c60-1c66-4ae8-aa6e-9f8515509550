<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzhuxn.personals.mapper.user.UserGiftMapper">
    <select id="queryUsedByIds" resultType="java.lang.String">
        SELECT
        DISTINCT gift_name
        FROM
        user_gift
        WHERE
        del_flag = 0
        AND gift_id IN
        <foreach collection="giftIds" item="giftId" open="(" separator="," close=")">
            #{giftId}
        </foreach>
    </select>
</mapper>
