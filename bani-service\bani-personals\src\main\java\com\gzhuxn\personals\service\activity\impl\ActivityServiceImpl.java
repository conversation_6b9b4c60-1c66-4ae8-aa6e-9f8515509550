package com.gzhuxn.personals.service.activity.impl;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.DateUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.controller.app.activity.bo.AppActivityBo;
import com.gzhuxn.personals.controller.app.activity.vo.AppActivityDetailFeeVo;
import com.gzhuxn.personals.controller.app.activity.vo.AppActivityDetailVo;
import com.gzhuxn.personals.controller.app.activity.vo.AppActivityPageVo;
import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.domain.activity.ActivityFee;
import com.gzhuxn.personals.domain.activity.bo.ActivityBo;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardStorageVo;
import com.gzhuxn.personals.domain.activity.vo.ActivityVo;
import com.gzhuxn.personals.domain.message.bo.MsgContentSendBo;
import com.gzhuxn.personals.enums.activity.ActivityFeeType;
import com.gzhuxn.personals.enums.activity.ActivityStatus;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.enums.message.MessageSubType;
import com.gzhuxn.personals.enums.message.MessageType;
import com.gzhuxn.personals.enums.user.follow.FollowType;
import com.gzhuxn.personals.event.activity.ActivityProducer;
import com.gzhuxn.personals.mapper.activity.ActivityMapper;
import com.gzhuxn.personals.service.activity.IActivityFeeService;
import com.gzhuxn.personals.service.activity.IActivityService;
import com.gzhuxn.personals.service.audit.IContentAuditRecordService;
import com.gzhuxn.personals.service.message.IMsgContentUserService;
import com.gzhuxn.personals.service.user.IUserFollowService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 活动-活动管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RequiredArgsConstructor
@Service
public class ActivityServiceImpl extends BaniServiceImpl<ActivityMapper, Activity> implements IActivityService {
    private final IActivityFeeService activityFeeService;
    private final IMsgContentUserService msgContentUserService;
    private final IUserFollowService userFollowService;
    private final ActivityProducer activityProducer;
    private final IContentAuditRecordService contentAuditRecordService;

    /**
     * 查询活动-活动管理
     *
     * @param id 主键
     * @return 活动-活动管理
     */
    @Override
    public ActivityVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询活动-活动管理
     *
     * @param id 主键
     * @return 活动-活动管理
     */
    @Override
    public AppActivityDetailVo queryAppById(Long id) {
        AppActivityDetailVo detailVo = baseMapper.selectVoById(id, AppActivityDetailVo.class);

        // 查询活动费用
        List<ActivityFee> fees = activityFeeService.listByActivityId(id);
        List<AppActivityDetailFeeVo> feeVos = MapstructUtils.convert(fees, AppActivityDetailFeeVo.class);
        detailVo.setFees(feeVos);

        // 保险信息
        List<ActSafeguardStorageVo> storageVos = fees.stream()
            // 保险
            .filter(fee -> fee.getType().equals(ActivityFeeType.SAFEGUARD.getValue()))
            // 转换为vos
            .map(fee -> JSONUtil.toBean(fee.getContentJs(), ActSafeguardStorageVo.class))
            .toList();
        detailVo.setSafeguards(storageVos);
        return detailVo;
    }

    /**
     * 分页查询活动-活动管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动-活动管理分页列表
     */
    @Override
    public TableDataInfo<ActivityVo> queryPageList(ActivityBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Activity> lqw = baseMapper.buildQueryWrapper(bo);
        Page<ActivityVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 分页查询活动-活动管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动-活动管理分页列表
     */
    @Override
    public TableDataInfo<AppActivityPageVo> queryAppPageList(ActivityBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Activity> lqw = baseMapper.buildQueryWrapper(bo);
        Page<AppActivityPageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, AppActivityPageVo.class);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的活动-活动管理列表
     *
     * @param bo 查询条件
     * @return 活动-活动管理列表
     */
    @Override
    public List<ActivityVo> queryList(ActivityBo bo) {
        LambdaQueryWrapper<Activity> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增活动-活动管理
     *
     * @param bo 活动-活动管理
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertByBo(AppActivityBo bo) {
        Activity add = MapstructUtils.convert(bo, Activity.class);
        beforeSave(add);
        boolean flag = save(add);

        // 保存后的数据校验
        afterSave(add, bo);
        return flag;
    }

    /**
     * 修改活动-活动管理
     *
     * @param bo 活动-活动管理
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByBo(AppActivityBo bo) {
        Activity oldActivity = existById(bo.getId());
        ActivityStatus activityStatus = ActivityStatus.of(oldActivity.getStatus());
        if (ActivityStatus.DRAFT != activityStatus && ActivityStatus.PUBLISHED != activityStatus) {
            AssertUtils.throwException("当前活动状态不可编辑！");
        }

        Activity update = MapstructUtils.convert(bo, Activity.class);
        beforeSave(update);
        boolean flag = updateById(update);

        // 保存后的数据校验
        afterSave(update, bo);
        return flag;
    }

    /**
     * 保存后的数据操作
     */
    private void afterSave(Activity entity, AppActivityBo bo) {
        activityFeeService.update(entity, bo.getFees());

        // 发起异步审核
        contentAuditRecordService.createAudit(entity.getId(), AuditType.ACTIVITY);
    }

    /**
     * 保存前的数据校验
     */
    private void beforeSave(Activity entity) {
        // 获取活动时长
        String timeLength = DateUtil.formatBetween(DateUtils
            .toDate(entity.getStartTime()), DateUtils.parseDate(entity.getEndTime()), BetweenFormatter.Level.MINUTE);
        entity.setTimeLength(timeLength);
        entity.setStatus(ActivityStatus.PUBLISHED.getValue());
        entity.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(Activity entity) {
        updateById(entity);
        // 触发活动报名开始延时事件
        activityProducer.send(entity, ActivityProducer.EventType.ENROLL_START);
    }

    @Override
    public void updateAuditFail(Activity activity) {
        updateById(activity);
    }


    @Override
    public boolean deleteWithValidByIds(Collection<Long> ids, boolean isValid) {
        if (isValid) {
            ids = baseMapper.listIdsByIdsAndUserId(ids, LoginHelper.getUserId());
            AssertUtils.notEmpty(ids, "未找到关联活动");
        }
        return super.removeBatchByIds(ids);
    }

    @Override
    public Activity existById(Long activityId) {
        return super.existById(activityId, "活动不存在");
    }


    @Override
    public void enrollStart(Activity activity) {
        activity.setStatus(ActivityStatus.ENROLL_START.getValue());
        updateById(activity);

        // 触发活动报名结束事件
        activityProducer.send(activity, ActivityProducer.EventType.ENROLL_END);

        // 发送报名通知
        // 获取已关注活动的用户ID列表
        List<Long> userIds = userFollowService.listUserIdByBusinessIdAndType(activity.getId(), FollowType.ACTIVITY);
        if (userIds.isEmpty()) {
            return;
        }
        String content = "活动【" + activity.getName() + "】报名已开始，快来报名吧！";
        MsgContentSendBo sendBo = new MsgContentSendBo();
        sendBo.setSendUserId(activity.getCreateBy());
        sendBo.setToUserIds(userIds);
        sendBo.setType(MessageType.RELATED);
        sendBo.setSubType(MessageSubType.RELATED_ACTIVITY_ENROLL);
        sendBo.setContent(content);
        sendBo.addParams("activityId", activity.getId());
        // 活动名称
        sendBo.addParams("activityName", activity.getName());
        // 活动开始时间
        sendBo.addParams("activityStartTime", DateUtil.format(activity.getStartTime(), "yyyy-MM-dd HH:mm"));
        sendBo.addParams("activityTimeLength", activity.getTimeLength());
        sendBo.addParams("activityAddr", activity.getAddr());
        msgContentUserService.send(sendBo);
    }

    @Override
    public void enrollEnd(Activity activity) {
        activity.setStatus(ActivityStatus.ENROLL_END.getValue());
        updateById(activity);

        // 触发活动开始事件
        activityProducer.send(activity, ActivityProducer.EventType.ACTIVITY_START);
    }

    @Override
    public void activityStart(Activity activity) {
        activity.setStatus(ActivityStatus.ACTIVITY_START.getValue());
        updateById(activity);

        // 触发活动结束事件
        activityProducer.send(activity, ActivityProducer.EventType.ACTIVITY_END);
    }

    @Override
    public void activityEnd(Activity activity) {
        activity.setStatus(ActivityStatus.ACTIVITY_END.getValue());
        updateById(activity);
    }

}
