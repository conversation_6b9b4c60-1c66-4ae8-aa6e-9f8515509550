package com.gzhuxn.personals.domain.gift.bo;

import com.gzhuxn.common.core.enums.EnableStatus;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.mybatis.core.domain.BaseEntity;
import com.gzhuxn.personals.domain.gift.GiftManage;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 礼物-礼物管理业务对象 gift_manage
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = GiftManage.class, reverseConvertGenerate = false)
public class GiftManageBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 礼物名称
     */
    @NotBlank(message = "礼物名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 礼物logo路径ID
     */
    @NotNull(message = "礼物logo路径ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long icon;

    /**
     * 价格：花瓣
     */
    @NotNull(message = "价格：花瓣不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer price;

    /**
     * 免费标识（0收费、1免费）
     */
    @NotNull(message = "免费标识（0收费、1免费）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer freeFlag;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer sort;

    /**
     * 可用状态（0不可用、1可用）
     *
     * @see EnableStatus
     */
    @NotNull(message = "可用状态（0不可用、1可用）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer status;
}
