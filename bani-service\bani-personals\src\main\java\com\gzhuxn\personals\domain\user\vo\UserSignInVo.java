package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserSignIn;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 用户-签到视图对象 user_sign_in
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserSignIn.class, convertGenerate = false)
public class UserSignInVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 签到日期
     */
    @ExcelProperty(value = "签到日期")
    private LocalDate date;

    /**
     * 连续签到天数
     */
    @ExcelProperty(value = "连续签到天数")
    private Integer consecutiveDays;

    /**
     * 获得花瓣
     */
    @ExcelProperty(value = "获得花瓣")
    private Integer coin;
}
