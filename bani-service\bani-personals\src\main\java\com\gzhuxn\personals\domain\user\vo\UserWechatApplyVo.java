package com.gzhuxn.personals.domain.user.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserWechatApply;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-微信申请视图对象 user_wechat_apply
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserWechatApply.class, convertGenerate = false)
public class UserWechatApplyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 对方用户ID
     */
    @ExcelProperty(value = "对方用户ID")
    private Long oppositeUserId;

    /**
     * 申请信息
     */
    @ExcelProperty(value = "申请信息")
    private String content;

    /**
     * 状态;0待发起、1申请中、2已通过、3已拒绝
     */
    @ExcelProperty(value = "状态;0待发起、1申请中、2已通过、3已拒绝")
    private Integer status;


}
