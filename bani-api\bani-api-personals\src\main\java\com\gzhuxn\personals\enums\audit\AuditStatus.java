package com.gzhuxn.personals.enums.audit;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核状态
 * 0待发起审核、1 待审核、2 通过、3 未通过、11转人工审核
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/16 17:50
 */
@Getter
@AllArgsConstructor
public enum AuditStatus {
    DRAFT(0, "草稿"),
    /**
     * 发起审核
     */
    WAIT_AUDIT(1, "待审核"),
    TRANSFER_WAIT_AUDIT(11, "转人工审核"),
    PASS(2, "通过"),
    REJECT(3, "拒绝"),
    ;
    private final Integer value;
    private final String desc;
}
