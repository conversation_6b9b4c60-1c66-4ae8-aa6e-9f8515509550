package com.gzhuxn.personals.service.audit.processor;

import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessor;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 活动-活动管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RequiredArgsConstructor
@Service
public class AuditTransferUserProcessor implements ContentAuditProcessor {

    @Override
    public AuditType getAuditType() {
        return AuditType.DEFAULT;
    }

    @Override
    public ContentAuditRes execute(Long businessId) {
        return ContentAuditRes.transferUser();
    }
}
