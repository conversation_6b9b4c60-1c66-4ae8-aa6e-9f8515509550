package com.gzhuxn.personals.enums.coin;

import com.gzhuxn.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 花瓣配置类型
 * <p>
 * 1签到
 * 101 第一天签到
 * 102 第二天签到
 * 103 第三天签到
 * 104 第四天签到
 * 105 第五天签到
 * 106 第六天签到
 * 107 第七天签到
 * <p>
 * 2新手任务
 * 认证申请
 * 201 实名认证
 * 202 学历认证
 * 203 车辆认证
 * 204 房屋认证
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/28 17:25
 */
@Getter
public enum CoinMgSubType {
    SIGN_IN(1, "签到"),
    AUTH_TYPE_1(101, "第一天签到"),
    AUTH_TYPE_2(102, "第二天签到"),
    AUTH_TYPE_3(103, "第三天签到"),
    AUTH_TYPE_4(104, "第四天签到"),
    AUTH_TYPE_5(105, "第五天签到"),
    AUTH_TYPE_6(106, "第六天签到"),
    AUTH_TYPE_7(107, "第七天签到"),

    NEW_TASK(2, "新手任务"),

    // 认证申请
    AUTH_APPLY(201, "认证申请"),
    AUTH_IDENTITY(201, "实名认证"),
    AUTH_EDUCATION(202, "学历认证"),
    AUTH_VEHICLE(203, "车辆认证"),
    AUTH_HOUSE(204, "房屋认证");

    private final Integer value;
    private final String desc;

    CoinMgSubType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     */
    public static CoinMgSubType of(Integer value) {
        for (CoinMgSubType item : values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        throw new ServiceException("花瓣配置类型不存在");
    }
}
