package com.gzhuxn.personals.domain.gift.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.common.core.enums.EnableStatus;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.gift.GiftManage;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 礼物-礼物管理视图对象 gift_manage
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GiftManage.class, convertGenerate = false)
public class GiftManageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 礼物名称
     */
    @ExcelProperty(value = "礼物名称")
    private String name;

    /**
     * 礼物logo路径ID
     */
    @ExcelProperty(value = "礼物logo路径ID")
    private Long icon;

    /**
     * 价格：花瓣
     */
    @ExcelProperty(value = "价格：花瓣")
    private Integer price;

    /**
     * 免费标识（0收费、1免费）
     */
    @NotNull(message = "免费标识（0收费、1免费）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer freeFlag;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 可用状态（0不可用、1可用）
     *
     * @see EnableStatus
     */
    @ExcelProperty(value = "可用状态（0不可用、1可用）")
    private Integer status;

    /**
     * 版本号
     */
    @ExcelProperty(value = "版本号")
    private String version;
}
