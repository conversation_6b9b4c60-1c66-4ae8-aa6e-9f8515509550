package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserComment;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-评论视图对象 user_comment
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserComment.class, convertGenerate = false)
public class UserCommentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 父评论ID
     */
    @ExcelProperty(value = "父评论ID")
    private Long parentId;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 类型：1用户动态、2活动
     */
    @ExcelProperty(value = "类型：1用户动态、2活动")
    private Integer type;

    /**
     * 关联业务ID
     */
    @ExcelProperty(value = "关联业务ID")
    private Long businessId;

    /**
     * 图片ID，最多6张
     */
    @ExcelProperty(value = "图片ID，最多6张")
    private String images;


}
