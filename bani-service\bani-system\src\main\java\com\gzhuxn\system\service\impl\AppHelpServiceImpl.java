package com.gzhuxn.system.service.impl;

import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.system.controller.admin.app.bo.AdminAppHelpStatusUpBo;
import com.gzhuxn.system.domain.AppHelp;
import com.gzhuxn.system.domain.bo.AppHelpBo;
import com.gzhuxn.system.domain.vo.AppHelpVo;
import com.gzhuxn.system.mapper.AppHelpMapper;
import com.gzhuxn.system.service.IAppHelpService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * app应用-常见问题帮助Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@RequiredArgsConstructor
@Service
public class AppHelpServiceImpl
    extends BaniServiceImpl<AppHelpMapper, AppHelp> implements IAppHelpService {

    /**
     * 查询app应用-常见问题帮助
     *
     * @param id 主键
     * @return app应用-常见问题帮助
     */
    @Override
    public AppHelpVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询app应用-常见问题帮助列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return app应用-常见问题帮助分页列表
     */
    @Override
    public TableDataInfo<AppHelpVo> queryPageList(AppHelpBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppHelp> lqw = baseMapper.buildQueryWrapper(bo);
        Page<AppHelpVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的app应用-常见问题帮助列表
     *
     * @param bo 查询条件
     * @return app应用-常见问题帮助列表
     */
    @Override
    public List<AppHelpVo> queryList(AppHelpBo bo) {
        LambdaQueryWrapper<AppHelp> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增app应用-常见问题帮助
     *
     * @param bo app应用-常见问题帮助
     * @return 是否新增成功
     */
    @Override
    public boolean insertByBo(AppHelpBo bo) {
        AppHelp add = MapstructUtils.convert(bo, AppHelp.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改app应用-常见问题帮助
     *
     * @param bo app应用-常见问题帮助
     * @return 是否修改成功
     */
    @Override
    public boolean updateByBo(AppHelpBo bo) {
        AppHelp update = MapstructUtils.convert(bo, AppHelp.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public boolean updateAdminStatus(AdminAppHelpStatusUpBo bo) {
        existById(bo.getId(), "常见问题不存在");
        AppHelp upHelp = new AppHelp();
        upHelp.setId(bo.getId());
        upHelp.setStatus(bo.getStatus());
        return updateById(upHelp);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppHelp entity) {
        // 解析协议内容base64转码
        String content = Base64.decodeStr(entity.getContent());
        entity.setContent(content);
    }

    /**
     * 校验并批量删除app应用-常见问题帮助信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }
}
