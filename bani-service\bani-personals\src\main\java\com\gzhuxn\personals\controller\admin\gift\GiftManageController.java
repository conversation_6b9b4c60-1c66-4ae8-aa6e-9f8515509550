package com.gzhuxn.personals.controller.admin.gift;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.gzhuxn.common.base.domain.DeleteBo;
import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.controller.admin.gift.bo.AdminGiftStatusUpBo;
import com.gzhuxn.personals.controller.admin.gift.vo.AdminGiftManageVo;
import com.gzhuxn.personals.domain.gift.bo.GiftManageBo;
import com.gzhuxn.personals.service.gift.IGiftManageService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 礼物-礼物管理
 * 前端访问路由地址为:/personals/manage
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/gift/manage")
public class GiftManageController extends BaseController {

    private final IGiftManageService giftManageService;

    /**
     * 查询礼物-礼物管理列表
     */
    @SaCheckPermission("personals:gift-manage:page")
    @GetMapping("/page")
    public TableDataInfo<AdminGiftManageVo> page(GiftManageBo bo, PageQuery pageQuery) {
        return giftManageService.queryAdminPageList(bo, pageQuery);
    }

    /**
     * 获取礼物-礼物管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("personals:gift-manage:detail")
    @GetMapping("/detail")
    public R<AdminGiftManageVo> detail(@NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(giftManageService.queryAdminById(id));
    }

    /**
     * 礼物管理-新增
     */
    @SaCheckPermission("personals:gift-manage:create")
    @Log(title = "礼物管理-新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody GiftManageBo bo) {
        return toAjax(giftManageService.insertByBo(bo));
    }

    /**
     * 礼物管理-修改
     */
    @SaCheckPermission("personals:gift-manage:update")
    @Log(title = "礼物管理-修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated(EditGroup.class) @RequestBody GiftManageBo bo) {
        return toAjax(giftManageService.updateByBo(bo));
    }

    /**
     * 礼物管理-修改状态
     */
    @SaCheckPermission("personals:gift-manage:update")
    @Log(title = "礼物管理-修改状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated(EditGroup.class) @RequestBody AdminGiftStatusUpBo bo) {
        return toAjax(giftManageService.updateAdminStatus(bo));
    }

    /**
     * 礼物管理-删除
     */
    @SaCheckPermission("personals:gift-manage:delete")
    @Log(title = "礼物管理-删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public R<Void> remove(@Valid @RequestBody DeleteBo bo) {
        return toAjax(giftManageService.deleteWithValidByIds(bo.getIds()));
    }
}
