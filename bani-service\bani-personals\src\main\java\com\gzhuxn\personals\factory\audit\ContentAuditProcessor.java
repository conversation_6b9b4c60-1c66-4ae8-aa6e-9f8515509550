package com.gzhuxn.personals.factory.audit;


import com.gzhuxn.personals.enums.audit.AuditType;

/**
 * 内容审核处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/16 22:33
 */
public interface ContentAuditProcessor {

    /**
     * 获取内容审核类型
     *
     * @return 内容审核类型
     */
    AuditType getAuditType();

    /**
     * 执行处理内容审核
     *
     * @param businessId 业务ID
     */
    ContentAuditRes execute(Long businessId);
}
