package com.gzhuxn.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * app应用-协议配置对象 app_agreement
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_agreement")
public class AppAgreement extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 协议名称
     */
    private String name;

    /**
     * 协议key
     */
    private String agreementKey;

    /**
     * 协议内容
     */
    private String content;

    /**
     * 备注
     */
    private String remark;

    /**
     * 协议版本
     */
    private String version;

    /**
     * 状态;0无效、1有效。备注：一个key 状态status=1时只能存在一条数据
     *
     * @see com.gzhuxn.common.core.enums.EnableStatus
     */
    private Integer status;
}
