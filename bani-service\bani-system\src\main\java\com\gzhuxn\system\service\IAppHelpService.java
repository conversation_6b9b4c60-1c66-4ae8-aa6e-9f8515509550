package com.gzhuxn.system.service;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.system.controller.admin.app.bo.AdminAppHelpStatusUpBo;
import com.gzhuxn.system.domain.bo.AppHelpBo;
import com.gzhuxn.system.domain.vo.AppHelpVo;

import java.util.Collection;
import java.util.List;

/**
 * app应用-常见问题帮助Service接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public interface IAppHelpService {

    /**
     * 查询app应用-常见问题帮助
     *
     * @param id 主键
     * @return app应用-常见问题帮助
     */
    AppHelpVo queryById(Long id);

    /**
     * 分页查询app应用-常见问题帮助列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return app应用-常见问题帮助分页列表
     */
    TableDataInfo<AppHelpVo> queryPageList(AppHelpBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的app应用-常见问题帮助列表
     *
     * @param bo 查询条件
     * @return app应用-常见问题帮助列表
     */
    List<AppHelpVo> queryList(AppHelpBo bo);

    /**
     * 新增app应用-常见问题帮助
     *
     * @param bo app应用-常见问题帮助
     * @return 是否新增成功
     */
    boolean insertByBo(AppHelpBo bo);

    /**
     * 修改app应用-常见问题帮助
     *
     * @param bo app应用-常见问题帮助
     * @return 是否修改成功
     */
    boolean updateByBo(AppHelpBo bo);

    /**
     * 修改app应用-常见问题帮助状态
     *
     * @param bo app应用-常见问题帮助
     * @return 是否修改成功
     */
    boolean updateAdminStatus(AdminAppHelpStatusUpBo bo);

    /**
     * 校验并批量删除app应用-常见问题帮助信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
