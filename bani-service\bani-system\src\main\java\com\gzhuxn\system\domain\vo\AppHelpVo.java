package com.gzhuxn.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.gzhuxn.system.domain.AppHelp;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * app应用-常见问题帮助视图对象 app_help
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AppHelp.class, convertGenerate = false)
public class AppHelpVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 帮助key
     */
    private String helpKey;

    /**
     * 名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 内容Html
     */
    private String content;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态;0无效、1有效。备注：一个key 状态status=1时只能存在一条数据
     *
     * @see com.gzhuxn.common.core.enums.EnableStatus
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
