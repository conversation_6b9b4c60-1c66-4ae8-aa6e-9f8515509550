package com.gzhuxn.personals.domain.group.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.group.GroupTag;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 群组-群组标签视图对象 grp_group_tag
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GroupTag.class, convertGenerate = false)
public class GroupTagVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 群组ID
     */
    @ExcelProperty(value = "群组ID")
    private Long groupId;

    /**
     * 标签值
     */
    @ExcelProperty(value = "标签值")
    private String val;

    /**
     * 标签值名称
     */
    @ExcelProperty(value = "标签值名称")
    private String name;


}
