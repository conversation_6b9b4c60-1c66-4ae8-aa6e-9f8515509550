package com.gzhuxn.personals.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.domain.pay.ToPayResult;
import com.gzhuxn.common.base.enums.PayStatus;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.StreamUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.controller.app.order.bo.AppCreateOrderBo;
import com.gzhuxn.personals.controller.app.user.bo.authapply.AuthIdentityPayBo;
import com.gzhuxn.personals.domain.UserPayBaseEntity;
import com.gzhuxn.personals.domain.user.UserAuthApply;
import com.gzhuxn.personals.domain.user.UserAuthPayRecord;
import com.gzhuxn.personals.domain.user.bo.UserAuthPayRecordBo;
import com.gzhuxn.personals.domain.user.vo.UserAuthPayRecordVo;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.enums.coin.CoinType;
import com.gzhuxn.personals.enums.order.OrderType;
import com.gzhuxn.personals.factory.pay.PayOrderProcessor;
import com.gzhuxn.personals.mapper.user.UserAuthPayRecordMapper;
import com.gzhuxn.personals.service.order.IUserOrderService;
import com.gzhuxn.personals.service.user.IUserAuthApplyService;
import com.gzhuxn.personals.service.user.IUserAuthPayRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 用户-实名认证支付记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class UserAuthPayRecordServiceImpl
    extends BaniServiceImpl<UserAuthPayRecordMapper, UserAuthPayRecord> implements IUserAuthPayRecordService, PayOrderProcessor<UserAuthPayRecord> {
    
    private final IUserAuthApplyService userAuthApplyService;
    private final IUserOrderService userOrderService;

    public UserAuthPayRecordServiceImpl(IUserAuthApplyService userAuthApplyService, @Lazy IUserOrderService userOrderService) {
        this.userAuthApplyService = userAuthApplyService;
        this.userOrderService = userOrderService;
    }

    /**
     * 查询用户-实名认证支付记录
     *
     * @param id 主键
     * @return 用户-实名认证支付记录
     */
    @Override
    public UserAuthPayRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户-实名认证支付记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-实名认证支付记录分页列表
     */
    @Override
    public TableDataInfo<UserAuthPayRecordVo> queryPageList(UserAuthPayRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserAuthPayRecord> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserAuthPayRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ToPayResult authIdentityPay(AuthIdentityPayBo bo) {
        UserAuthApply authApply = validateAuthApply(bo);

        // 构建用户-实名认证支付记录
        UserAuthPayRecord entity = build(bo, authApply);
        save(entity);

        // 构建支付订单
        AppCreateOrderBo orderBo = AppCreateOrderBo.builder().userId(bo.getUserId())
            .originalAmount(bo.getAmount())
            .amount(bo.getAmount())
            .businessId(entity.getId())
            .exchangeCoin(bo.getCoin())
            .build();
        // 发起支付
        ToPayResult payResult = userOrderService.createOrder(orderBo, getOrderType(), CoinType.GIFT);

        // 更新订单ID
        entity.setOrderId(payResult.getPayOrderId());
        updateById(entity);
        return payResult;
    }

    /**
     * 构建用户-实名认证支付记录
     *
     * @param bo        订单信息
     * @param authApply 认证申请信息
     * @return 用户-实名认证支付记录
     */
    private UserAuthPayRecord build(AuthIdentityPayBo bo, UserAuthApply authApply) {
        UserAuthPayRecord entity = new UserAuthPayRecord();
        entity.setAuthApplyId(bo.getAuthApplyId());
        entity.setAuthType(bo.getAuthType());
        entity.setUserId(bo.getUserId());
        entity.setOriginalAmount(bo.getAmount());
        entity.setAmount(bo.getAmount());
        entity.setCoin(bo.getCoin());
        entity.setPayStatus(PayStatus.WAIT_PAY.getValue());
        return entity;
    }

    /**
     * 校验认证申请状态
     *
     * @param bo 订单信息
     * @return 认证申请信息
     */
    private UserAuthApply validateAuthApply(AuthIdentityPayBo bo) {
        UserAuthApply authApply = userAuthApplyService.existById(bo.getAuthApplyId());
        AuditStatus auditStatus = AuditStatus.of(authApply.getAuditStatus());
        if (AuditStatus.WAIT_SUBMIT != auditStatus) {
            AssertUtils.throwException("当前认证申请" + auditStatus.getDesc() + "不能进行支付！");
        }
        
        // 验证认证类型
        AuditType auditType = AuditType.of(bo.getAuthType());
        if (!auditType.isAuthApply()) {
            AssertUtils.throwException("认证类型错误！");
        }
        
        AssertUtils.isTrue(authApply.getType().equals(bo.getAuthType()), "认证类型不匹配！");
        
        return authApply;
    }

    @Override
    public OrderType getOrderType() {
        return OrderType.AUTH_IDENTITY;
    }

    @Override
    public Optional<UserAuthPayRecord> queryByOrderId(Long orderId) {
        return Optional.ofNullable(baseMapper.getByOrderId(orderId));
    }

    @Override
    public void paySuccess(UserPayBaseEntity entity) {
        log.info("实名认证订单支付成功，id={}", entity.getId());
        updateById((UserAuthPayRecord) entity);
    }

    @Override
    public void close(UserPayBaseEntity subEntity) {
        log.info("实名认证订单关闭操作成功，id={}", subEntity.getId());
        updateById((UserAuthPayRecord) subEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        Long userId = null;
        if (isValid) {
            userId = LoginHelper.getUserId();
        }
        List<UserAuthPayRecord> records = baseMapper.listByIdsAndUserId(ids, userId);
        if (records.isEmpty()) {
            return false;
        }
        List<Long> orderIds = StreamUtils.toList(records, UserAuthPayRecord::getOrderId);
        userOrderService.removeBatchByIds(orderIds);
        return removeBatchByIds(ids);
    }

    @Override
    public void delete(Long businessId) {
        removeById(businessId);
    }
}
