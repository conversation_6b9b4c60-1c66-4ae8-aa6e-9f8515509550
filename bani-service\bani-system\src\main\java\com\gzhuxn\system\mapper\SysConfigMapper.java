package com.gzhuxn.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.system.domain.SysConfig;
import com.gzhuxn.system.domain.vo.SysConfigVo;

/**
 * 参数配置 数据层
 *
 * <AUTHOR>
 */
public interface SysConfigMapper extends BaseMapperPlus<SysConfig, SysConfigVo> {

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数键名
     * @return 参数配置信息
     */
    default SysConfig selectConfigByKey(String configKey) {
        return selectOne(new LambdaQueryWrapper<SysConfig>()
            .eq(SysConfig::getConfigKey, configKey));
    }
}
