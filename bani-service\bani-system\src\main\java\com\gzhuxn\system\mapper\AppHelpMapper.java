package com.gzhuxn.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.core.utils.StringUtils;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.system.domain.AppHelp;
import com.gzhuxn.system.domain.bo.AppHelpBo;
import com.gzhuxn.system.domain.vo.AppHelpVo;

/**
 * app应用-常见问题帮助Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public interface AppHelpMapper extends BaseMapperPlus<AppHelp, AppHelpVo> {

    /**
     * 构建查询条件
     */
    default LambdaQueryWrapper<AppHelp> buildQueryWrapper(AppHelpBo bo) {
        LambdaQueryWrapper<AppHelp> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), AppHelp::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), AppHelp::getContent, bo.getContent());
        lqw.eq(bo.getSort() != null, AppHelp::getSort, bo.getSort());
        return lqw;
    }
}
