package com.gzhuxn.personals.controller.app.activity.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardStorageVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 活动-活动管理视图对象 act_activity
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Activity.class)
public class AppActivityDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 报名开始时间
     */
    private LocalDateTime enrollStartTime;

    /**
     * 报名结束时间
     */
    private LocalDateTime enrollEndTime;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动时长（天/时/分）
     */
    private String timeLength;

    /**
     * 退款截止时间
     */
    private LocalDateTime refundTime;

    /**
     * 所在地址ID
     */
    private String addrId;

    /**
     * 所在地址名称
     */
    private String addrName;

    /**
     * 详细地址
     */
    private String addr;

    /**
     * 是否是官方活动：0否，1是
     */
    private Integer officialFlag;

    /**
     * 活动介绍
     */
    private String introduce;

    /**
     * 原费用
     */
    private BigDecimal originalAmount;

    /**
     * 现费用
     */
    private BigDecimal amount;

    /**
     * 活动状态;1草稿，2已发布/未开始、1报名中、2报名已结束、10活动进行中、11活动已结束
     */
    private Integer status;

    /**
     * 审核状态;0未发起审核、1审核中、2审核通过、3审核被拒绝
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核描述
     */
    private String auditDesc;

    /**
     * 类型：;1相亲会、2聊天、3干饭、4户外、5看展、6运动、7学习、8喝酒、9打游戏、10其他
     */
    private Integer type;

    /**
     * 位置经度
     */
    private Double lon;

    /**
     * 位置纬度
     */
    private Double lat;

    /**
     * 创建人/发起人名称
     */
    private String createByName;

    /**
     * 保险
     */
    private List<ActSafeguardStorageVo> safeguards;

    /**
     * 费用
     */
    private List<AppActivityDetailFeeVo> fees;
}
