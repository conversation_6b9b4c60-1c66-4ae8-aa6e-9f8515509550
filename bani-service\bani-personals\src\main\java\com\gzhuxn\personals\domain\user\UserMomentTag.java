package com.gzhuxn.personals.domain.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.personals.domain.UserBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户-动态标签对象 user_moment_tag
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_moment_tag")
public class UserMomentTag extends UserBaseEntity {

    /**
     * 动态ID
     */
    private Long momentId;

    /**
     * 话题ID
     */
    private Long tagVal;

    /**
     * 话题名称
     */
    private String tagValName;
}
