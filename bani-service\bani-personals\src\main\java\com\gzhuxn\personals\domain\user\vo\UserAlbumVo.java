package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.common.translation.annotation.Translation;
import com.gzhuxn.common.translation.constant.TransConstant;
import com.gzhuxn.personals.domain.user.UserAlbum;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-相册视图对象 user_album
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserAlbum.class, convertGenerate = false)
public class UserAlbumVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 相册文件ID
     */
    @Translation(type = TransConstant.TO_FILE_OSS)
    @ExcelProperty(value = "相册文件ID")
    private Long fileId;


}
