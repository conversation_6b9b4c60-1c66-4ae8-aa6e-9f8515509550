package com.gzhuxn.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * app应用-常见问题帮助对象 app_help
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_help")
public class AppHelp extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 帮助key
     */
    private String helpKey;

    /**
     * 名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 内容Html
     */
    private String content;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态;0无效、1有效。备注：一个key 状态status=1时只能存在一条数据
     *
     * @see com.gzhuxn.common.core.enums.EnableStatus
     */
    private Integer status;
}
