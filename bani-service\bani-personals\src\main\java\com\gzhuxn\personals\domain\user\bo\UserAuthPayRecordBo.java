package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.user.UserAuthPayRecord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 用户-实名认证支付记录业务对象 user_auth_pay_record
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserAuthPayRecord.class, reverseConvertGenerate = false)
public class UserAuthPayRecordBo extends BsBo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 认证申请ID
     */
    @NotNull(message = "认证申请ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long authApplyId;

    /**
     * 认证类型;101-实名认证、102-学历认证、103-车辆认证、104-房屋认证
     */
    @NotNull(message = "认证类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer authType;

    /**
     * 原费用
     */
    private BigDecimal originalAmount;

    /**
     * 实际支付金额
     */
    @NotNull(message = "支付金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal amount;

    /**
     * 兑换花瓣数量
     */
    private Integer coin;

    /**
     * 支付状态;1待支付、2支付中、3支付失败、4关闭、10支付成功
     */
    private Integer payStatus;

    /**
     * 支付订单ID
     */
    private Long orderId;
}
