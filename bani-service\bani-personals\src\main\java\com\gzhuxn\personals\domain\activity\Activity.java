package com.gzhuxn.personals.domain.activity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.gzhuxn.common.base.domain.entity.BsEntity;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动-活动管理对象 act_activity
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("act_activity")
public class Activity extends BsEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 活动ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 封面图片
     */
    private Long avatar;

    /**
     * 报名开始时间
     */
    private LocalDateTime enrollStartTime;

    /**
     * 报名结束时间
     */
    private LocalDateTime enrollEndTime;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动时长（天/时/分）
     */
    private String timeLength;

    /**
     * 退款截止时间
     */
    private LocalDateTime refundTime;

    /**
     * 户籍地省编码
     */
    private String addrProvinceCode;

    /**
     * 户籍地市编码
     */
    private String addrCityCode;

    /**
     * 户籍地区/县编码
     */
    private String addrDistrictCode;

    /**
     * 户籍地街道/镇/乡编码
     */
    private String addrStreetCode;

    /**
     * 详细地址
     */
    private String addr;

    /**
     * 是否是官方活动：0否，1是
     */
    private Integer officialFlag;

    /**
     * 活动介绍
     */
    private String introduce;

    /**
     * 兑换花瓣数量
     */
    private Integer coin;

    /**
     * 报名人数上限
     */
    private Integer limitNum;

    /**
     * 原费用
     */
    private BigDecimal originalAmount;

    /**
     * 现费用
     */
    private BigDecimal amount;

    /**
     * 活动状态;1草稿，2已发布/未开始、3报名中、4报名已结束、10活动进行中、11活动已结束
     *
     * @see com.gzhuxn.personals.enums.activity.ActivityStatus
     */
    private Integer status;

    /**
     * 审核状态
     *
     * @see AuditStatus
     */
    private Integer auditStatus;

    /**
     * 类型：;1相亲会、2聊天、3干饭、4户外、5看展、6运动、7学习、8喝酒、9打游戏、10其他
     *
     * @see com.gzhuxn.personals.enums.activity.ActivityTypeVal
     */
    private Integer type;

    /**
     * 位置经度
     */
    private Double lon;

    /**
     * 位置纬度
     */
    private Double lat;

    /**
     * 浏览量
     */
    private Integer pv;
    /**
     * 点赞量
     */
    private Integer lv;
    /**
     * 评论量
     */
    private Integer cv;

    /**
     * 创建人/发起人名称
     */
    private String createByName;

    // --------------- 扩展字段 ---------------
    /**
     * 已报名人数（已支付）
     */
    private Integer enrollNum;

    @Version
    private Integer version;
}
