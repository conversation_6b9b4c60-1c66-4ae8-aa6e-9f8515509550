package com.gzhuxn.personals.domain.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.personals.domain.UserPayBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户-实名认证支付记录对象 user_auth_pay_record
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_auth_pay_record")
public class UserAuthPayRecord extends UserPayBaseEntity {

    /**
     * 认证申请ID
     */
    private Long authApplyId;

    /**
     * 认证类型;101-实名认证、102-学历认证、103-车辆认证、104-房屋认证
     */
    private Integer authType;
}
