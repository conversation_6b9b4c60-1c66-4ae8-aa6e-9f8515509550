package com.gzhuxn.personals.enums.order;

import lombok.Getter;

/**
 * 支付订单类型
 * <p>
 * 支付订单类型：1充值、2购买会员
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 14:30
 */
@Getter
public enum OrderType {
    /**
     * 充值
     */
    RECHARGE(1, "充值"),
    VIP(2, "购买会员"),
    ACTIVITY(3, "活动报名");

    private final Integer value;
    private final String desc;

    OrderType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据value获取枚举
     *
     * @param value v
     * @return r
     */
    public static OrderType of(Integer value) {
        for (OrderType payOrderType : OrderType.values()) {
            if (payOrderType.getValue().equals(value)) {
                return payOrderType;
            }
        }
        throw new IllegalArgumentException("支付订单类型不存在=" + value);
    }
}
