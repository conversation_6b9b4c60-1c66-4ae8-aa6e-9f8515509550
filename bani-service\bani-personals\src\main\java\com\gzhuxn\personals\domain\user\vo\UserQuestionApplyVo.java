package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserQuestionApply;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-我发起的问答视图对象 user_question_apply
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserQuestionApply.class, convertGenerate = false)
public class UserQuestionApplyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 回答用户ID
     */
    @ExcelProperty(value = "回答用户ID")
    private Long oppositeUserId;

    /**
     * 问题名称
     */
    @ExcelProperty(value = "问题名称")
    private String name;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 回答内容
     */
    @ExcelProperty(value = "回答内容")
    private String answerContent;

    /**
     * 回答状态;：0待发起、1待回答、2已回答、3已拒绝
     */
    @ExcelProperty(value = "回答状态;：0待发起、1待回答、2已回答、3已拒绝")
    private Integer status;


}
