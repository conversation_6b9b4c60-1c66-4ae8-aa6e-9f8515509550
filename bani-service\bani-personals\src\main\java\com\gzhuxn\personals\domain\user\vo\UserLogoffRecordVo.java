package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserLogoffRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 用户-用户注销记录视图对象 user_logoff_record
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserLogoffRecord.class, convertGenerate = false)
public class UserLogoffRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 状态;0注销中、1注销成功
     */
    @ExcelProperty(value = "状态;0注销中、1注销成功")
    private Integer status;

    /**
     * 预计注销时间
     */
    @ExcelProperty(value = "预计注销时间")
    private LocalDateTime logoffTime;

    /**
     * 实际注销时间
     */
    @ExcelProperty(value = "实际注销时间")
    private LocalDateTime succeedTime;


}
