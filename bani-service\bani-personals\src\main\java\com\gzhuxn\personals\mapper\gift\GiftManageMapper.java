package com.gzhuxn.personals.mapper.gift;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.core.utils.StringUtils;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.gift.GiftManage;
import com.gzhuxn.personals.domain.gift.bo.GiftManageBo;
import com.gzhuxn.personals.domain.gift.vo.GiftManageVo;

/**
 * 礼物-礼物管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface GiftManageMapper extends BaseMapperPlus<GiftManage, GiftManageVo> {

    default LambdaQueryWrapper<GiftManage> buildQueryWrapper(GiftManageBo bo) {
        LambdaQueryWrapper<GiftManage> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), GiftManage::getName, bo.getName());
        lqw.eq(null != bo.getIcon(), GiftManage::getIcon, bo.getIcon());
        lqw.eq(bo.getPrice() != null, GiftManage::getPrice, bo.getPrice());
        lqw.eq(bo.getFreeFlag() != null, GiftManage::getFreeFlag, bo.getFreeFlag());
        lqw.orderByAsc(GiftManage::getSort);
        return lqw;
    }
}
