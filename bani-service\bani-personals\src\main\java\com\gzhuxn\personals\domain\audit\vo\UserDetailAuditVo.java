package com.gzhuxn.personals.domain.audit.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.gzhuxn.personals.domain.user.UserDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-用户详情审核视图对象 user_detail
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserDetail.class, convertGenerate = false)
public class UserDetailAuditVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


}
