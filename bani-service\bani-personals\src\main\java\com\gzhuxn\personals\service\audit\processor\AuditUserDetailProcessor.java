package com.gzhuxn.personals.service.audit.processor;

import com.gzhuxn.personals.domain.user.UserDetail;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessor;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;
import com.gzhuxn.personals.service.user.IUserDetailService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 用户-用户详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class AuditUserDetailProcessor implements ContentAuditProcessor {
    @Resource
    private IUserDetailService userDetailService;

    @Override
    public AuditType getAuditType() {
        return AuditType.USER_BASE_INFO;
    }

    @Override
    public ContentAuditRes execute(Long businessId) {
        UserDetail userDetail = userDetailService.getByUserId(businessId);
        return null;
    }
}
