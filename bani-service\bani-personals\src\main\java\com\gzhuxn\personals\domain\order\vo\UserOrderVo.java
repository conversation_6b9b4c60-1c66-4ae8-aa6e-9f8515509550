package com.gzhuxn.personals.domain.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.order.UserOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 用户-支付记录视图对象 user_pay_order
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserOrder.class, convertGenerate = false)
public class UserOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 充值金额
     */
    @ExcelProperty(value = "充值金额")
    private Integer amount;

    /**
     * 兑换花瓣数量
     */
    @ExcelProperty(value = "兑换花瓣数量")
    private Integer exchangeCoin;

    /**
     * 支付状态;1待支付、2支付中、3支付失败、10支付成功
     */
    @ExcelProperty(value = "支付状态;1待支付、2支付中、3支付失败、10支付成功")
    private Integer status;

    /**
     * 支付成功时间
     */
    @ExcelProperty(value = "支付成功时间")
    private LocalDateTime payTime;

    /**
     * 失败信息
     */
    @ExcelProperty(value = "失败信息")
    private String failMsg;

    /**
     * 失败描述
     */
    @ExcelProperty(value = "失败描述")
    private String failDesc;


}
