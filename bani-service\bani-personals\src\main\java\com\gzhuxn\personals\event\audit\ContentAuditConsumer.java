package com.gzhuxn.personals.event.audit;

import com.github.likavn.eventbus.core.annotation.EventbusListener;
import com.github.likavn.eventbus.core.api.MsgListener;
import com.github.likavn.eventbus.core.metadata.data.Message;
import com.gzhuxn.personals.domain.audit.ContentAuditRecord;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessor;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessorFactory;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;
import com.gzhuxn.personals.service.audit.IContentAuditRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 内容审核事件消费者
 * <p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/7 22:51
 */
@Slf4j
@Component
@EventbusListener
public class ContentAuditConsumer implements MsgListener<ContentAuditProducer.ContentAuditEvent> {
    @Lazy
    @Resource
    private ContentAuditProcessorFactory processorFactory;
    @Resource
    private IContentAuditRecordService contentAuditRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(Message<ContentAuditProducer.ContentAuditEvent> event) {
        ContentAuditProducer.ContentAuditEvent body = event.getBody();
        ContentAuditRecord auditRecord = contentAuditRecordService.selectById(body.getId());
        if (null == auditRecord) {
            log.warn("未找到id=[{}]对应的审核记录！", body.getId());
            return;
        }
        Optional<ContentAuditProcessor> processor = processorFactory.getProcessor(body.getAuditType());
        if (processor.isEmpty()) {
            log.warn("{}审核未找到对应的处理器！", body.getAuditType().getDesc());
            return;
        }
        // 处理
        ContentAuditRes res = processor.get().execute(body.getBusinessId());

        // 转人工审核
        if (res.isTransferUser()) {
            contentAuditRecordService.transferUser(auditRecord);
            return;
        }
        if (res.isPass()) {
            contentAuditRecordService.pass(auditRecord, res);
        } else {
            contentAuditRecordService.reject(auditRecord, res);
        }
    }
}
