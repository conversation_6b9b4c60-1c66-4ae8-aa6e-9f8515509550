package com.gzhuxn.personals.service.gift.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.enums.EnableStatus;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.controller.admin.gift.bo.AdminGiftStatusUpBo;
import com.gzhuxn.personals.controller.admin.gift.vo.AdminGiftManageVo;
import com.gzhuxn.personals.controller.app.gift.vo.AppGiftManageVo;
import com.gzhuxn.personals.domain.gift.GiftManage;
import com.gzhuxn.personals.domain.gift.bo.GiftManageBo;
import com.gzhuxn.personals.domain.gift.vo.GiftManageVo;
import com.gzhuxn.personals.mapper.gift.GiftManageMapper;
import com.gzhuxn.personals.service.gift.IGiftManageService;
import com.gzhuxn.personals.service.user.IUserGiftService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 礼物-礼物管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class GiftManageServiceImpl extends BaniServiceImpl<GiftManageMapper, GiftManage> implements IGiftManageService {
    @Lazy
    @Resource
    private IUserGiftService userGiftService;

    @Override
    public GiftManage getById(Long giftId) {
        return existById(giftId);
    }

    /**
     * 查询礼物-礼物管理
     *
     * @param id 主键
     * @return 礼物-礼物管理
     */
    @Override
    public AdminGiftManageVo queryAdminById(Long id) {
        return baseMapper.selectVoById(id, AdminGiftManageVo.class);
    }

    /**
     * 分页查询礼物-礼物管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 礼物-礼物管理分页列表
     */
    @Override
    public TableDataInfo<AdminGiftManageVo> queryAdminPageList(GiftManageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GiftManage> lqw = baseMapper.buildQueryWrapper(bo);
        Page<AdminGiftManageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, AdminGiftManageVo.class);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的礼物-礼物管理列表
     *
     * @param bo 查询条件
     * @return 礼物-礼物管理列表
     */
    @Override
    public List<GiftManageVo> queryList(GiftManageBo bo) {
        LambdaQueryWrapper<GiftManage> lqw = baseMapper.buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增礼物-礼物管理
     *
     * @param bo 礼物-礼物管理
     * @return 是否新增成功
     */
    @Override
    public boolean insertByBo(GiftManageBo bo) {
        GiftManage add = MapstructUtils.convert(bo, GiftManage.class);
        validEntityBeforeSave(add);
        add.setVersion("1.0");
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改礼物-礼物管理
     *
     * @param bo 礼物-礼物管理
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByBo(GiftManageBo bo) {
        GiftManage update = MapstructUtils.convert(bo, GiftManage.class);
        validEntityBeforeSave(update);
        // 老版本禁用
        update.setStatus(EnableStatus.DISABLE.getValue());
        return updateById(update);
//        // 旧版本
//        GiftManage oldBean = getById(update.getId());
//        AssertUtils.notNull(oldBean, "数据不存在！");
//
//        // 创建新版本
//        update.setId(null);
//        update.setStatus(EnableStatus.ENABLE.getValue());
//        // 版本号升级
//        double v = Double.parseDouble(oldBean.getVersion()) + 0.1;
//        update.setVersion(String.valueOf(v));
//        return baseMapper.insert(update) > 0;
    }

    @Override
    public boolean updateAdminStatus(AdminGiftStatusUpBo bo) {
        existById(bo.getId(), "礼物不存在");
        GiftManage update = new GiftManage();
        update.setId(bo.getId());
        update.setStatus(bo.getStatus());
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GiftManage entity) {
        if (null == entity) {
            return;
        }
        GiftManage gift = getOne(Wrappers.<GiftManage>lambdaQuery().eq(GiftManage::getName, entity.getName()).last("LIMIT 1"));
        if (null == gift) {
            return;
        }
        if (null == entity.getId()) {
            throw new IllegalArgumentException("同名称礼物已存在，请确认！");
        }
        if (!entity.getId().equals(gift.getId())) {
            throw new IllegalArgumentException("同名称礼物已存在，请确认！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWithValidByIds(List<Long> ids) {
        List<String> gifts = userGiftService.queryUsedByIds(ids);
        AssertUtils.isTrue(gifts.isEmpty(), "选中礼物[%s]已使用，不可删除！".formatted(String.join("、", gifts)));
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<AppGiftManageVo> queryAppList() {
        LambdaQueryWrapper<GiftManage> lqw = Wrappers.lambdaQuery();
        lqw.eq(GiftManage::getStatus, EnableStatus.ENABLE.getValue());
        lqw.orderByAsc(GiftManage::getSort);
        return baseMapper.selectVoList(lqw, AppGiftManageVo.class);
    }
}
