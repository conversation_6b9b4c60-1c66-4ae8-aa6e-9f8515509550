package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserAccount;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-账户信息视图对象 user_account
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserAccount.class, convertGenerate = false)
public class UserAccountVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long userId;

    /**
     * 剩余花瓣数
     */
    @ExcelProperty(value = "剩余花瓣数")
    private Integer coin;

    /**
     * 充值花瓣总数
     */
    @ExcelProperty(value = "充值花瓣总数")
    private Integer coinTotal;

    /**
     * 已使用花瓣总数
     */
    @ExcelProperty(value = "已使用花瓣总数")
    private Integer coinUseTotal;
}
