package com.gzhuxn.personals.domain.message.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.message.MsgContentUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 消息-消息用户关系视图对象 msg_content_user
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MsgContentUser.class, convertGenerate = false)
public class MsgContentUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 关联用户ID
     */
    @ExcelProperty(value = "关联用户ID")
    private Long userId;

    /**
     * 消息内容ID
     */
    @ExcelProperty(value = "消息内容ID")
    private Long contentId;

    /**
     * 类型
     *
     * @see com.gzhuxn.personals.enums.message.MessageType
     */
    @ExcelProperty(value = "类型")
    private Integer type;

    /**
     * 子类型
     *
     * @see com.gzhuxn.personals.enums.message.MessageSubType
     */
    @ExcelProperty(value = "子类型")
    private Integer subType;

    /**
     * 是否已读;0未读、1已读
     */
    @ExcelProperty(value = "是否已读;0未读、1已读")
    private Integer read;
}
