package com.gzhuxn.system.domain.bo;

import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.system.domain.AppAgreement;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * app应用-协议配置业务对象 app_agreement
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AppAgreement.class, reverseConvertGenerate = false)
public class AppAgreementBo extends BsBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 协议名称
     */
    @NotBlank(message = "协议名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 协议key
     */
    @NotBlank(message = "协议key不能为空", groups = {AddGroup.class, EditGroup.class})
    private String agreementKey;

    /**
     * 协议内容
     */
    @NotBlank(message = "协议内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 状态;0无效、1有效。备注：一个key 状态status=1时只能存在一条数据
     */
    @NotNull(message = "状态;0无效、1有效。备注：一个key 状态status=1时只能存在一条数据不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer status;
}
