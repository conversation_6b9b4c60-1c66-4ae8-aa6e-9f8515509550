package com.gzhuxn.personals.domain.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.personals.domain.UserBaseEntity;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户-动态对象 user_moment
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_moment")
public class UserMoment extends UserBaseEntity {

    /**
     * 内容
     */
    private String content;

    /**
     * 图片ID，最多6张
     */
    private String images;

    /**
     * 状态
     *
     * @see com.gzhuxn.personals.enums.user.moment.MomentStatus
     */
    private Integer status;

    /**
     * 审核状态
     *
     * @see AuditStatus
     */
    private Integer auditStatus;

    /**
     * 浏览量
     */
    private Integer pv;
    /**
     * 点赞量
     */
    private Integer lv;
    /**
     * 评论量
     */
    private Integer cv;
}
