-- 为user_auth_pay_record表添加use_status字段的迁移脚本
ALTER TABLE `user_auth_pay_record` 
ADD COLUMN `use_status` int(11) NOT NULL DEFAULT '0' COMMENT '使用状态;0未使用、1已使用' AFTER `order_id`;

-- 添加索引
ALTER TABLE `user_auth_pay_record` 
ADD INDEX `idx_use_status` (`use_status`);

-- 添加复合索引，用于快速查询用户指定认证类型的未使用且已支付成功的记录
ALTER TABLE `user_auth_pay_record` 
ADD INDEX `idx_user_auth_type` (`user_id`, `auth_type`, `use_status`, `pay_status`);
