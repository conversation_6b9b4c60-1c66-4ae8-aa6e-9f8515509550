package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserMomentTag;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-动态标签视图对象 user_moment_tag
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserMomentTag.class, convertGenerate = false)
public class UserMomentTagVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 动态ID
     */
    @ExcelProperty(value = "动态ID")
    private Long momentId;

    /**
     * 话题ID
     */
    @ExcelProperty(value = "话题ID")
    private String tagVal;

    /**
     * 话题名称
     */
    @ExcelProperty(value = "话题名称")
    private String tagValName;


}
