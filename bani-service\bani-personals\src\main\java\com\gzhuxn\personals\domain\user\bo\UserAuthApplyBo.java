package com.gzhuxn.personals.domain.user.bo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gzhuxn.common.base.domain.BsBo;
import com.gzhuxn.common.core.validate.AddGroup;
import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.user.UserAuthApply;
import com.gzhuxn.personals.enums.audit.AuditType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户-认证申请业务对象 user_auth_apply
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserAuthApply.class, reverseConvertGenerate = false)
public class UserAuthApplyBo extends BsBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 认证类型;101-实名认证、2-学历认证、3-车辆认证、4-房屋认证
     *
     * @see AuditType
     */
    @NotNull(message = "认证类型;101-实名认证、2-学历认证、3-车辆认证、4-房屋认证", groups = {AddGroup.class, EditGroup.class})
    private Integer type;

    /**
     * 认证状态;1待认证、2已认证、3认证失败
     */
    @NotNull(message = "认证状态;0待认证、1已认证、2认证失败不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer status;

    /**
     * 失败描述
     */
    @NotBlank(message = "失败描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String auditDesc;

    /**
     * 认证账户ID
     */
    @NotNull(message = "认证账户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long authUserId;

    /**
     * 认证时间
     */
    @NotNull(message = "认证时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private LocalDateTime authTime;

    /**
     * Json数据
     */
    @NotBlank(message = "Json数据不能为空", groups = {AddGroup.class, EditGroup.class})
    private String contentJs;

    /**
     * 实名认证实体
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2025/4/8 22:10
     */
    @Data
    public static class AuthIdentityBo {
        /**
         * 用户ID
         */
        @JsonIgnore
        private Long userId;
        /**
         * 姓名
         */
        @NotBlank(message = "姓名不能为空", groups = AddGroup.class)
        private String name;
        /**
         * 身份证号码
         */
        @NotBlank(message = "姓名不能为空", groups = AddGroup.class)
        private String idCard;
        /**
         * 人脸图片OSS文件ID
         */
        @NotNull(message = "人脸图片不能为空", groups = AddGroup.class)
        private Long ossId;
    }

    /**
     * 学历认证实体
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2025/4/8 22:10
     */
    @Data
    public static class AuthEducationBo {
        /**
         * 用户ID
         */
        @JsonIgnore
        private Long userId;
        /**
         * 毕业院校
         */
        @NotNull(message = "毕业院校不能为空", groups = AddGroup.class)
        private String schoolName;
        /**
         * 学历,编码;字典：user_edu
         */
        @NotNull(message = "学历编码不能为空", groups = AddGroup.class)
        private String edu;
        /**
         * 学历
         */
        @NotNull(message = "学历不能为空", groups = AddGroup.class)
        private String eduName;
        /**
         * 图片OSS文件ID
         */
        @NotNull(message = "图片不能为空", groups = AddGroup.class)
        private List<Long> ossIds;
    }

    /**
     * 车辆认证实体
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2025/4/8 22:10
     */
    @Data
    public static class AuthCarBo {
        /**
         * 用户ID
         */
        @JsonIgnore
        private Long userId;
        /**
         * 品牌名称
         */
        @NotNull(message = "品牌名称不能为空", groups = AddGroup.class)
        private String brandName;
        /**
         * 车牌号
         */
        @NotNull(message = "车牌号不能为空", groups = AddGroup.class)
        private String plateNumber;
        /**
         * 图片OSS文件ID
         */
        @NotNull(message = "图片不能为空", groups = AddGroup.class)
        private List<Long> ossIds;
    }

    /**
     * 房认证实体
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2025/4/8 22:10
     */
    @Data
    public static class AuthHouseBo {
        /**
         * 用户ID
         */
        @JsonIgnore
        private Long userId;
        /**
         * 图片OSS文件ID
         */
        @NotNull(message = "图片不能为空", groups = AddGroup.class)
        private List<Long> ossIds;
    }

}
