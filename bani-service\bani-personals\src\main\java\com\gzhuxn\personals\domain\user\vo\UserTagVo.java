package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserTag;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-标签视图对象 user_tag
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserTag.class, convertGenerate = false)
public class UserTagVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 标签命名空间;profile
     */
    @ExcelProperty(value = "标签命名空间;profile")
    private String namespace;

    /**
     * 标签Key
     */
    @ExcelProperty(value = "标签Key")
    private String tagKey;

    /**
     * 标签值
     */
    @ExcelProperty(value = "标签值")
    private String tagVal;

    /**
     * 标签值名称
     */
    @ExcelProperty(value = "标签值名称")
    private String tagValName;


}
