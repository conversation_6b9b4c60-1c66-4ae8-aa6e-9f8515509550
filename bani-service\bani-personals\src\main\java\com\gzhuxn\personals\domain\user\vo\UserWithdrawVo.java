package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserWithdraw;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 用户-提现申请视图对象 user_withdraw
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserWithdraw.class, convertGenerate = false)
public class UserWithdrawVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 提现用户ID
     */
    @ExcelProperty(value = "提现用户ID")
    private Long userId;

    /**
     * 提现金额
     */
    @ExcelProperty(value = "提现金额")
    private BigDecimal amount;

    /**
     * 收款人收款二维码图片ID
     */
    @ExcelProperty(value = "收款人收款二维码图片ID")
    private Long withdrawQrCodeImage;
    /**
     * 审核状态;1发起申请、2审核通过、3打回补充、4拒绝
     */
    @ExcelProperty(value = "审核状态;1发起申请、2审核通过、3打回补充、4拒绝")
    private Integer auditStatus;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    /**
     * 审核人
     */
    @ExcelProperty(value = "审核人")
    private Long auditUserId;

    /**
     * 汇款状态;1待汇款、2已汇款
     */
    @ExcelProperty(value = "汇款状态;1待汇款、2已汇款")
    private Integer remitStatus;

    /**
     * 汇款时间
     */
    @ExcelProperty(value = "汇款时间")
    private LocalDateTime remitTime;
}
