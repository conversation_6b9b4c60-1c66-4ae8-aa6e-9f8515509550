package com.gzhuxn.personals.domain.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.personals.domain.UserBaseEntity;
import com.gzhuxn.personals.enums.audit.AuditType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户-认证申请对象 user_auth_apply
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@TableName("user_auth_apply")
@EqualsAndHashCode(callSuper = true)
public class UserAuthApply extends UserBaseEntity {

    /**
     * 认证类型;101-实名认证、102-学历认证、103-车辆认证、104-房屋认证
     *
     * @see AuditType
     */
    private Integer type;

    /**
     * 0待发起审核、1 待审核、2 通过、3 未通过、11转人工审核
     *
     * @see com.gzhuxn.personals.enums.audit.AuditStatus
     */
    private Integer auditStatus;

    /**
     * 失败描述
     */
    private String auditDesc;

    /**
     * 认证账户ID
     */
    private Long authUserId;

    /**
     * 认证时间
     */
    private LocalDateTime authTime;

    /**
     * Json数据
     */
    private String contentJs;
}
