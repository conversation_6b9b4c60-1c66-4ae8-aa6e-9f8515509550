package com.gzhuxn.personals.domain.gift;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.common.base.domain.entity.BsEntity;
import com.gzhuxn.common.core.enums.EnableStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 礼物-礼物管理对象 gift_manage
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("gift_manage")
public class GiftManage extends BsEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 礼物名称
     */
    private String name;

    /**
     * 礼物logo路径ID
     */
    private Long icon;

    /**
     * 价格：花瓣
     */
    private Integer price;

    /**
     * 免费标识（0收费、1免费）
     */
    private Integer freeFlag;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 可用状态（0不可用、1可用）
     *
     * @see EnableStatus
     */
    private Integer status;

    /**
     * 版本号
     */
    private String version;
}
