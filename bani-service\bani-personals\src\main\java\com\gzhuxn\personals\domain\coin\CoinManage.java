package com.gzhuxn.personals.domain.coin;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.common.base.domain.entity.BsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 花瓣-花瓣赠送管理对象 coin_manage
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("coin_manage")
public class CoinManage extends BsEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 类型;
     * 1.签到
     * 2.新手任务
     * @see com.gzhuxn.personals.enums.coin.CoinMgType
     */
    private Integer type;

    /**
     * 子类型
     * @see com.gzhuxn.personals.enums.coin.CoinMgSubType
     */
    private Integer subType;

    /**
     * 名称
     */
    private String name;

    /**
     * 花瓣数量
     */
    private Integer coin;
}
