package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserAccountHistory;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-账号信息历史记录视图对象 user_account_history
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserAccountHistory.class, convertGenerate = false)
public class UserAccountHistoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 业务类型;1充值、2礼物、3微信申请、4问答
     */
    @ExcelProperty(value = "业务类型;1充值、2礼物、3微信申请、4问答")
    private Integer type;

    /**
     * 关联业务ID
     */
    @ExcelProperty(value = "关联业务ID")
    private Long businessId;

    /**
     * 消费花瓣数量
     */
    @ExcelProperty(value = "消费花瓣数量")
    private Integer use;

    /**
     * 消费前花瓣数量
     */
    @ExcelProperty(value = "消费前花瓣数量")
    private Integer useBefore;

    /**
     * 消费花瓣后数量
     */
    @ExcelProperty(value = "消费花瓣后数量")
    private Integer useAfter;
}
