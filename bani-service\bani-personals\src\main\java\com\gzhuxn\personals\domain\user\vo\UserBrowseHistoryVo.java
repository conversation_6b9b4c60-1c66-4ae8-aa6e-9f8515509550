package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserBrowseHistory;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-主页浏览历史记录视图对象 user_browse_history
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserBrowseHistory.class, convertGenerate = false)
public class UserBrowseHistoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 浏览用户ID
     */
    @ExcelProperty(value = "浏览用户ID")
    private Long oppositeUserId;

    /**
     * 浏览类型;1用户主页、2活动、3用户动态
     */
    @ExcelProperty(value = "浏览类型;1用户主页、2活动、3用户动态")
    private Integer type;

    /**
     * 业务ID
     */
    @ExcelProperty(value = "业务ID")
    private Long businessId;


}
