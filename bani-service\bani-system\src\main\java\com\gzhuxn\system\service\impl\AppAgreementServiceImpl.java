package com.gzhuxn.system.service.impl;

import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.enums.EnableStatus;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.system.controller.admin.app.bo.AdminAppAgreementStatusUpBo;
import com.gzhuxn.system.controller.admin.app.vo.AdminAppAgreementPageVo;
import com.gzhuxn.system.domain.AppAgreement;
import com.gzhuxn.system.domain.bo.AppAgreementBo;
import com.gzhuxn.system.domain.vo.AppAgreementVo;
import com.gzhuxn.system.mapper.AppAgreementMapper;
import com.gzhuxn.system.service.IAppAgreementService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * app应用-协议配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@RequiredArgsConstructor
@Service
public class AppAgreementServiceImpl
    extends BaniServiceImpl<AppAgreementMapper, AppAgreement> implements IAppAgreementService {

    /**
     * 查询基础-协议配置
     *
     * @param id 主键
     * @return 基础-协议配置
     */
    @Override
    public AppAgreementVo queryAdminById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询基础-协议配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 基础-协议配置分页列表
     */
    @Override
    public TableDataInfo<AdminAppAgreementPageVo> queryAdminPageList(AppAgreementBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppAgreement> lqw = baseMapper.buildQueryWrapper(bo);
        Page<AdminAppAgreementPageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, AdminAppAgreementPageVo.class);
        return TableDataInfo.build(result);
    }


    /**
     * 新增基础-协议配置
     *
     * @param bo 基础-协议配置
     * @return 是否新增成功
     */
    @Override
    public boolean insertAdminByBo(AppAgreementBo bo) {
        AppAgreement add = MapstructUtils.convert(bo, AppAgreement.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改基础-协议配置
     *
     * @param bo 基础-协议配置
     * @return 是否修改成功
     */
    @Override
    public boolean updateAdminByBo(AppAgreementBo bo) {
        AppAgreement update = MapstructUtils.convert(bo, AppAgreement.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public boolean updateAdminStatus(AdminAppAgreementStatusUpBo bo) {
        existById(bo.getId(), "协议配置不存在");
        AppAgreement upAgreement = new AppAgreement();
        upAgreement.setId(bo.getId());
        upAgreement.setStatus(bo.getStatus());
        return updateById(upAgreement);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppAgreement entity) {
        // 解析协议内容base64转码
        String content = Base64.decodeStr(entity.getContent());
        entity.setContent(content);
        // 校验协议配置名称是否唯一
        boolean flag = baseMapper.validateAgreementKeyUnique(entity);
        AssertUtils.isTrue(flag, "协议配置名称已存在");
    }

    /**
     * 校验并批量删除基础-协议配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public AppAgreementVo getProtocolContentByKey(String key) {
        return baseMapper.selectVoOne(Wrappers.<AppAgreement>lambdaQuery()
            .select(AppAgreement::getId, AppAgreement::getName, AppAgreement::getAgreementKey, AppAgreement::getContent)
            .eq(AppAgreement::getAgreementKey, key)
            .eq(AppAgreement::getStatus, EnableStatus.ENABLE.getValue())
            .orderByDesc(AppAgreement::getCreateTime)
            .last("limit 1")
        );
    }
}
