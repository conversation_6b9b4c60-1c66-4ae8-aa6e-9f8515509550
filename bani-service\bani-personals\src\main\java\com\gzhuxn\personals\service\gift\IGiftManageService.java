package com.gzhuxn.personals.service.gift;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.controller.admin.gift.bo.AdminGiftStatusUpBo;
import com.gzhuxn.personals.controller.admin.gift.vo.AdminGiftManageVo;
import com.gzhuxn.personals.controller.app.gift.vo.AppGiftManageVo;
import com.gzhuxn.personals.domain.gift.GiftManage;
import com.gzhuxn.personals.domain.gift.bo.GiftManageBo;
import com.gzhuxn.personals.domain.gift.vo.GiftManageVo;

import java.util.List;

/**
 * 礼物-礼物管理Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IGiftManageService {

    /**
     * 根据主键查询礼物-礼物管理
     *
     * @param giftId 主键
     * @return 礼物-礼物管理
     */
    GiftManage getById(Long giftId);

    /**
     * 查询礼物-礼物管理
     *
     * @param id 主键
     * @return 礼物-礼物管理
     */
    AdminGiftManageVo queryAdminById(Long id);

    /**
     * 分页查询礼物-礼物管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 礼物-礼物管理分页列表
     */
    TableDataInfo<AdminGiftManageVo> queryAdminPageList(GiftManageBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的礼物-礼物管理列表
     *
     * @param bo 查询条件
     * @return 礼物-礼物管理列表
     */
    List<GiftManageVo> queryList(GiftManageBo bo);

    /**
     * 新增礼物-礼物管理
     *
     * @param bo 礼物-礼物管理
     * @return 是否新增成功
     */
    boolean insertByBo(GiftManageBo bo);

    /**
     * 修改礼物-礼物管理
     *
     * @param bo 礼物-礼物管理
     * @return 是否修改成功
     */
    boolean updateByBo(GiftManageBo bo);

    /**
     * 修改礼物-礼物管理状态
     *
     * @param bo 礼物-礼物管理
     * @return 是否修改成功
     */
    boolean updateAdminStatus(AdminGiftStatusUpBo bo);

    /**
     * 校验并批量删除礼物-礼物管理信息
     *
     * @param ids 待删除的主键集合
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(List<Long> ids);

    /**
     * 获取礼物列表
     *
     * @return 礼物列表
     */
    List<AppGiftManageVo> queryAppList();
}
