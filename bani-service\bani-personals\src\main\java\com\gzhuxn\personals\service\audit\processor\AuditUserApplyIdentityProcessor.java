package com.gzhuxn.personals.service.audit.processor;

import com.gzhuxn.common.json.utils.JsonUtils;
import com.gzhuxn.personals.domain.user.UserAuthApply;
import com.gzhuxn.personals.domain.user.bo.UserAuthApplyBo;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.factory.audit.ContentAuditProcessor;
import com.gzhuxn.personals.factory.audit.ContentAuditRes;
import com.gzhuxn.personals.service.user.IUserAuthApplyService;
import com.gzhuxn.resource.api.RemoteContentAuditService;
import com.gzhuxn.resource.api.domain.RemoteCheckIdentity;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 活动-活动管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RequiredArgsConstructor
@Service
public class AuditUserApplyIdentityProcessor implements ContentAuditProcessor {
    @DubboReference
    private RemoteContentAuditService remoteContentAuditService;

    @Resource
    private IUserAuthApplyService userAuthApplyService;

    @Override
    public AuditType getAuditType() {
        return AuditType.AUTH_APPLY_IDENTITY;
    }

    @Override
    public ContentAuditRes execute(Long businessId) {
        UserAuthApply apply = userAuthApplyService.existById(businessId);
        UserAuthApplyBo.AuthIdentityBo identityBo = JsonUtils.parseObject(apply.getContentJs(), UserAuthApplyBo.AuthIdentityBo.class);
        if (identityBo == null) {
            return null;
        }
        RemoteCheckIdentity r = remoteContentAuditService.checkIdentity(identityBo.getName(), identityBo.getIdCard(), identityBo.getOssId());
        if (r.isPass()) {
            return ContentAuditRes.isOk();
        }
        // 认证失败
        return ContentAuditRes.reject(r.getReason());
    }
}
