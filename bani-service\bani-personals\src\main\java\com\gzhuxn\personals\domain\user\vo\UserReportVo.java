package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserReport;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-举报视图对象 user_report
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserReport.class, convertGenerate = false)
public class UserReportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 举报类型;1用户、2活动、3群组
     */
    @ExcelProperty(value = "举报类型;1用户、2活动、3群组")
    private Integer type;

    /**
     * 关联业务ID
     */
    @ExcelProperty(value = "关联业务ID")
    private Long businessId;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 图片ID，最多6张
     */
    @ExcelProperty(value = "图片ID，最多6张")
    private String images;

    /**
     * 处理状态;1待处理、2已处理
     */
    @ExcelProperty(value = "处理状态;1待处理、2已处理")
    private Integer status;


}
