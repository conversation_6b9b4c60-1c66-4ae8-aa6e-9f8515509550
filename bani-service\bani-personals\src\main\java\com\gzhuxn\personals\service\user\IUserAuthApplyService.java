package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.user.UserAuthApply;
import com.gzhuxn.personals.domain.user.bo.UserAuthApplyBo;
import com.gzhuxn.personals.domain.user.vo.UserAuthApplyVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户-认证申请Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IUserAuthApplyService {

    /**
     * 查询用户-认证申请
     *
     * @param id 主键
     * @return 用户-认证申请
     */
    UserAuthApplyVo queryById(Long id);

    /**
     * 分页查询用户-认证申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户-认证申请分页列表
     */
    TableDataInfo<UserAuthApplyVo> queryAdminPageList(UserAuthApplyBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户-认证申请列表
     *
     * @param bo 查询条件
     * @return 用户-认证申请列表
     */
    List<UserAuthApplyVo> queryList(UserAuthApplyBo bo);

    /**
     * 新增身份认证
     *
     * @param bo 身份认证
     * @return 是否新增成功
     */
    boolean createIdentity(UserAuthApplyBo.AuthIdentityBo bo);

    /**
     * 新增学历认证
     *
     * @param bo 学历认证
     * @return 是否新增成功
     */
    boolean createEducation(UserAuthApplyBo.AuthEducationBo bo);

    /**
     * 新增车辆认证
     *
     * @param bo 车辆认证
     * @return 是否新增成功
     */
    boolean createCar(UserAuthApplyBo.AuthCarBo bo);

    /**
     * 新增房屋认证
     *
     * @param bo 房产认证
     * @return 是否新增成功
     */
    boolean createHouse(UserAuthApplyBo.AuthHouseBo bo);

    /**
     * 修改用户-认证申请
     *
     * @param bo 用户-认证申请
     * @return 是否修改成功
     */
    boolean updateByBo(UserAuthApplyBo bo);

    /**
     * 校验并批量删除用户-认证申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 认证成功
     */
    void pass(Long id);

    /**
     * 认证拒绝
     */
    void reject(Long id, String reason);

    /**
     * 根据id查询用户-认证申请信息
     *
     * @param id 主键
     * @return 用户-认证申请信息
     */
    UserAuthApply existById(Long id);
}
