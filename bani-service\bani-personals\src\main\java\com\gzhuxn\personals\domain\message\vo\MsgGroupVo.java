package com.gzhuxn.personals.domain.message.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.message.MsgGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 消息-群聊组视图对象 msg_group
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MsgGroup.class, convertGenerate = false)
public class MsgGroupVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 群聊人数
     */
    @ExcelProperty(value = "群聊人数")
    private Long num;

    /**
     * 类型：1活动群聊、2社群群聊
     */
    @ExcelProperty(value = "类型：1活动群聊、2社群群聊")
    private Integer type;

    /**
     * 关联业务ID
     */
    @ExcelProperty(value = "关联业务ID")
    private Long businessId;

    /**
     * 状态：1正常、2禁言、3解散
     */
    @ExcelProperty(value = "状态：1正常、2禁言、3解散")
    private Integer status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
