package com.gzhuxn.personals.domain.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.personals.domain.UserBaseEntity;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户-评论对象 user_comment
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_comment")
public class UserComment extends UserBaseEntity {
    /**
     * 父评论ID
     */
    private Long parentId;

    /**
     * 类型：1用户动态、2活动
     */
    private Integer type;

    /**
     * 关联业务ID
     */
    private Long businessId;

    /**
     * 内容
     */
    private String content;

    /**
     * 图片ID，最多6张
     */
    private String images;

    /**
     * 审核状态
     *
     * @see AuditStatus
     */
    private Integer auditStatus;
}
