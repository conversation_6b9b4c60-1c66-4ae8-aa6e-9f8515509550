package com.gzhuxn.personals.domain.audit;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.personals.domain.UserBaseEntity;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 审核-审核记录对象 audit_record
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("content_audit_record")
public class ContentAuditRecord extends UserBaseEntity {

    /**
     * 业务ID
     */
    private Long businessId;
    /**
     * 业务类型
     *
     * @see com.gzhuxn.personals.enums.audit.AuditType
     */
    private Integer type;

    /**
     * 审核状态
     *
     * @see AuditStatus
     */
    private Integer auditStatus;
    /**
     * 审核时
     */
    private LocalDateTime auditTime;
    /**
     * 审核描述
     */
    private String auditDesc;
    /**
     * 审核人
     */
    private Long auditUserId;
}
