package com.gzhuxn.personals.domain.user.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.common.excel.annotation.ExcelDictFormat;
import com.gzhuxn.common.excel.convert.ExcelDictConvert;
import com.gzhuxn.personals.domain.user.UserGift;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-发出的礼物视图对象 user_gift
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserGift.class, convertGenerate = false)
public class UserGiftVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 对方用户ID
     */
    @ExcelProperty(value = "对方用户ID")
    private Long oppositeUserId;

    /**
     * 礼物ID
     */
    @ExcelProperty(value = "礼物ID")
    private Long giftId;

    /**
     * 礼物名称
     */
    @ExcelProperty(value = "礼物名称")
    private String giftName;

    /**
     * 礼物价格（花瓣）
     */
    @ExcelProperty(value = "礼物价格", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "金=币")
    private Integer giftPrice;

    /**
     * 礼物数量
     */
    @ExcelProperty(value = "礼物数量")
    private Integer giftNum;

    /**
     * 消费花瓣总数量
     */
    @ExcelProperty(value = "消费花瓣总数量")
    private Integer coin;
}
