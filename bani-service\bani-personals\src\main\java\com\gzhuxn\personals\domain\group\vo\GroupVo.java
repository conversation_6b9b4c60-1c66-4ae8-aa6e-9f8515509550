package com.gzhuxn.personals.domain.group.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.group.Group;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 群组-群组管理视图对象 grp_group
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Group.class, convertGenerate = false)
public class GroupVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 群组名称
     */
    @ExcelProperty(value = "群组名称")
    private String name;

    /**
     * 头像ID
     */
    @ExcelProperty(value = "头像ID")
    private Long avatar;

    /**
     * 所在地址ID
     */
    @ExcelProperty(value = "所在地址ID")
    private String addrId;

    /**
     * 所在地址名称
     */
    @ExcelProperty(value = "所在地址名称")
    private String addrName;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    private String addr;

    /**
     * 介绍
     */
    @ExcelProperty(value = "介绍")
    private String introduce;

    /**
     * 背景图片ID
     */
    @ExcelProperty(value = "背景图片ID")
    private Long backgroundImg;

    /**
     * 是否是官方群组：0否，1是
     */
    @ExcelProperty(value = "是否是官方群组：0否，1是")
    private Integer officialFlag;

    /**
     * 群主用户ID
     */
    @ExcelProperty(value = "群主用户ID")
    private Long ownerUserId;


}
