package com.gzhuxn.personals.domain.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.personals.domain.UserBaseEntity;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户-我发起的问答对象 user_question_apply
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_question_apply")
public class UserQuestionApply extends UserBaseEntity {

    /**
     * 回答用户ID
     */
    private Long oppositeUserId;

    /**
     * 问题名称
     */
    private String name;

    /**
     * 内容
     */
    private String content;

    /**
     * 问答发起时间
     */
    private LocalDateTime sendTime;

    /**
     * 回答内容
     */
    private String answerContent;

    /**
     * 回答时间
     */
    private LocalDateTime answerTime;

    /**
     * 问答花瓣数
     */
    private Integer coin;

    /**
     * 回答状态;：0待发起、1待回答、2已回答、3已拒绝
     *
     * @see com.gzhuxn.personals.enums.user.question.QuestionApplyStatus
     */
    private Integer status;

    /**
     * 审核状态
     *
     * @see AuditStatus
     */
    private Integer auditStatus;
}
