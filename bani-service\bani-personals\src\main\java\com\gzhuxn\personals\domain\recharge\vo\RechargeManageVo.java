package com.gzhuxn.personals.domain.recharge.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.recharge.RechargeManage;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 充值-充值管理视图对象 recharge_manage
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RechargeManage.class, convertGenerate = false)
public class RechargeManageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 原充值费用
     */
    @ExcelProperty(value = "原充值费用")
    private BigDecimal originalAmount;

    /**
     * 充值费用
     */
    @ExcelProperty(value = "充值费用")
    private BigDecimal amount;

    /**
     * 折扣率,各位数
     */
    @ExcelProperty(value = "折扣率,各位数")
    private Integer discountRate;

    /**
     * 折扣类型：1限时折扣、2数量限制
     */
    @ExcelProperty(value = "折扣类型：1限时折扣、2数量限制")
    private Integer discountType;

    /**
     * 限时折扣过期时间
     */
    @ExcelProperty(value = "限时折扣过期时间")
    private LocalDateTime discountExpireTime;

    /**
     * 数量限制折扣数量
     */
    @ExcelProperty(value = "数量限制折扣数量")
    private Integer discountLimitNum;

    /**
     * 已购买数量
     */
    @ExcelProperty(value = "已购买数量")
    private Integer rechargeNum;

    /**
     * 兑换花瓣
     */
    @ExcelProperty(value = "兑换花瓣")
    private Integer coin;


}
