package com.gzhuxn.personals.service.coin;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.domain.coin.bo.CoinManageBo;
import com.gzhuxn.personals.domain.coin.vo.CoinManageVo;

import java.util.Collection;
import java.util.List;

/**
 * 花瓣-花瓣赠送管理Service接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface ICoinManageService {

    /**
     * 查询花瓣-花瓣赠送管理
     *
     * @param id 主键
     * @return 花瓣-花瓣赠送管理
     */
    CoinManageVo queryById(Long id);

    /**
     * 分页查询花瓣-花瓣赠送管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 花瓣-花瓣赠送管理分页列表
     */
    TableDataInfo<CoinManageVo> queryPageList(CoinManageBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的花瓣-花瓣赠送管理列表
     *
     * @param bo 查询条件
     * @return 花瓣-花瓣赠送管理列表
     */
    List<CoinManageVo> queryList(CoinManageBo bo);

    /**
     * 新增花瓣-花瓣赠送管理
     *
     * @param bo 花瓣-花瓣赠送管理
     * @return 是否新增成功
     */
    Boolean insertByBo(CoinManageBo bo);

    /**
     * 修改花瓣-花瓣赠送管理
     *
     * @param bo 花瓣-花瓣赠送管理
     * @return 是否修改成功
     */
    Boolean updateByBo(CoinManageBo bo);

    /**
     * 校验并批量删除花瓣-花瓣赠送管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据类型和子类型查询花瓣配置
     *
     * @param type    类型
     * @param subType 子类型
     * @return 花瓣配置
     */
    CoinManageVo queryByTypeAndSubType(Integer type, Integer subType);
}
