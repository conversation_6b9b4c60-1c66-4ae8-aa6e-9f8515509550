package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserFollow;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户-用户关注视图对象 user_follow
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserFollow.class, convertGenerate = false)
public class UserFollowVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 类型;:1关注我的、2我关注她的
     */
    @ExcelProperty(value = "类型;:1关注我的、2我关注她的")
    private Integer type;

    /**
     * 关联业务Id
     */
    @ExcelProperty(value = "关联业务Id")
    private Long businessId;


}
