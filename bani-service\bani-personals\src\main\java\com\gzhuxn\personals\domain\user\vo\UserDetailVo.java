package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserDetail;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 用户-用户详情视图对象 user_detail
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserDetail.class, convertGenerate = false)
public class UserDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户号
     */
    @ExcelProperty(value = "用户号")
    private Integer pid;

    /**
     * 生日/出生日期
     */
    @ExcelProperty(value = "生日/出生日期")
    private LocalDate birthday;

    /**
     * 星座,中文
     */
    @ExcelProperty(value = "星座,中文")
    private String star;

    /**
     * 十二生肖属相,中文
     */
    @ExcelProperty(value = "十二生肖属相,中文")
    private String animal;

    /**
     * 身高,cm
     */
    @ExcelProperty(value = "身高,cm")
    private Integer height;

    /**
     * 体重,kg
     */
    @ExcelProperty(value = "体重,kg")
    private Integer weight;

    /**
     * 学历,编码;字典：user_edu_val
     */
    @ExcelProperty(value = "学历,编码;字典：user_edu_val")
    private String edu;

    /**
     * 职业,编码;字典：user_job_val
     */
    @ExcelProperty(value = "职业,编码;字典：user_job_val")
    private String job;

    /**
     * 婚况,编码;字典：user_marriage_val
     */
    @ExcelProperty(value = "婚况,编码;字典：user_marriage_val")
    private String marriage;

    /**
     * 居住状况,编码;字典：user_house_val
     */
    @ExcelProperty(value = "居住状况,编码;字典：user_house_val")
    private String house;

    /**
     * 收入,编码;字典：user_revenue_val
     */
    @ExcelProperty(value = "收入,编码;字典：user_revenue_val")
    private String revenue;

    /**
     * 个人微信号
     */
    @ExcelProperty(value = "个人微信号")
    private String wechat;

    /**
     * 户籍地ID
     */
    @ExcelProperty(value = "户籍地ID")
    private String addrId;

    /**
     * 户籍地名称
     */
    @ExcelProperty(value = "户籍地名称")
    private String addrName;

    /**
     * 户籍详细地址
     */
    @ExcelProperty(value = "户籍详细地址")
    private String addr;

    /**
     * 现居地ID
     */
    @ExcelProperty(value = "现居地ID")
    private String addrNewId;

    /**
     * 现居地名称
     */
    @ExcelProperty(value = "现居地名称")
    private String addrNewName;

    /**
     * 现居详细地址
     */
    @ExcelProperty(value = "现居详细地址")
    private String addrNew;

    /**
     * 资料完善进度,百分比
     */
    @ExcelProperty(value = "资料完善进度,百分比")
    private Integer progress;

    /**
     * 审核状态
     *
     * @see AuditStatus
     */
    private Integer auditStatus;

    /**
     * 最新位置经度
     */
    @ExcelProperty(value = "最新位置经度")
    private Double lon;

    /**
     * 最新位置纬度
     */
    @ExcelProperty(value = "最新位置纬度")
    private Double lat;


}
