package com.gzhuxn.personals.domain.activity.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.activity.ActivityFee;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 活动-活动费用视图对象 act_activity_fee
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ActivityFee.class, convertGenerate = false)
public class ActivityFeeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 费用类型：1保障费用、10活动费用
     */
    @ExcelProperty(value = "费用类型：1保障费用、10活动费用")
    private Integer type;

    /**
     * 业务ID
     */
    @ExcelProperty(value = "业务ID")
    private Long businessId;

    /**
     * 费用
     */
    @ExcelProperty(value = "费用")
    private BigDecimal amount;

    /**
     * 费用内容JSON数据
     */
    @ExcelProperty(value = "费用内容JSON数据")
    private String contentJs;


}
