package com.gzhuxn.personals.domain.coin.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.coin.CoinManage;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 花瓣-花瓣赠送管理视图对象 coin_manage
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CoinManage.class, convertGenerate = false)
public class CoinManageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 类型;1.签到
     * 2.新手任务
     */
    @ExcelProperty(value = "类型;1.签到 2.新手任务")
    private Long type;

    /**
     * 子类型
     */
    @ExcelProperty(value = "子类型")
    private Long subType;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 花瓣数量
     */
    @ExcelProperty(value = "花瓣数量")
    private Long coin;
}
